import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as userController from '../controllers/user.controller';

const router = Router();

router.get('/', authenticateJWT, authorize<PERSON><PERSON>(['admin']), userController.getAllUsers);
router.get('/:id', authenticateJWT, userController.getUserById);
router.post('/', authenticateJWT, authorize<PERSON>ole(['admin']), userController.createUser);
router.put('/:id', authenticateJWT, authorize<PERSON>ole(['admin', 'user']), userController.updateUser);
router.delete('/:id', authenticateJWT, authorize<PERSON><PERSON>(['admin']), userController.deleteUser);

export default router; 