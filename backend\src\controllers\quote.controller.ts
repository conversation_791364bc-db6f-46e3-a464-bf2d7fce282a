import { Request, Response } from 'express';
import * as quoteService from '../services/quote.service';

export async function getAllQuotes(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await quoteService.getAllQuotes(page, limit);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getQuoteById(req: Request, res: Response) {
  try {
    const quote = await quoteService.getQuoteById(req.params.id);
    if (!quote) return res.status(404).json({ error: 'Devis non trouvé' });
    res.json(quote);
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createQuote(req: Request, res: Response) {
  try {
    const id = await quoteService.createQuote(req.body);
    res.status(201).json({ message: 'Devis créé', id });
  } catch (error: any) {
    if (error.message === 'Validation failed') {
      return res.status(400).json({ error: 'Validation', details: error.errors });
    }
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function updateQuote(req: Request, res: Response) {
  try {
    const affected = await quoteService.updateQuote(req.params.id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Devis non trouvé' });
    res.json({ message: 'Devis modifié' });
  } catch (error: any) {
    if (error.message === 'Validation failed') {
      return res.status(400).json({ error: 'Validation', details: error.errors });
    }
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function deleteQuote(req: Request, res: Response) {
  try {
    const affected = await quoteService.deleteQuote(req.params.id);
    if (affected === 0) return res.status(404).json({ error: 'Devis non trouvé' });
    res.json({ message: 'Devis supprimé' });
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}