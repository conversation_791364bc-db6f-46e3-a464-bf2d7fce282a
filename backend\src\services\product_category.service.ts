import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const categorySchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  description: z.string().optional(),
  parent_id: z.number().optional(),
  sort_order: z.number().optional(),
  is_active: z.boolean().optional(),
});

export const getAllCategories = async (page: number = 1, limit: number = 10) => {
  return paginateQuery('product_categories', page, limit);
};

export const getCategoryById = async (id: number) => {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM product_categories WHERE id = ?', [id]);
  return rows[0] || null;
};

export const createCategory = async (data: z.infer<typeof categorySchema>) => {
  const validatedData = categorySchema.parse(data);
  const db = connectDB();
  const [result] = await db.query<OkPacket>(
    'INSERT INTO product_categories (name, description, parent_id, sort_order, is_active) VALUES (?, ?, ?, ?, ?)',
    [
      validatedData.name,
      validatedData.description,
      validatedData.parent_id,
      validatedData.sort_order,
      validatedData.is_active,
    ]
  );
  return { id: result.insertId, ...validatedData };
};

export const updateCategory = async (id: number, data: Partial<z.infer<typeof categorySchema>>) => {
  const validatedData = categorySchema.partial().parse(data);
  const db = connectDB();
  const updates = [];
  const values = [];
  for (const [key, value] of Object.entries(validatedData)) {
    if (value !== undefined) {
      updates.push(`${key} = ?`);
      values.push(value);
    }
  }
  if (updates.length === 0) return null;
  values.push(id);
  const [result] = await db.query<OkPacket>(`UPDATE product_categories SET ${updates.join(', ')} WHERE id = ?`, values);
  if (result.affectedRows === 0) return null;
  return getCategoryById(id);
};

export const deleteCategory = async (id: number) => {
  const db = connectDB();
  const [result] = await db.query<OkPacket>('DELETE FROM product_categories WHERE id = ?', [id]);
  return { message: 'Catégorie supprimée avec succès', affectedRows: result.affectedRows };
};