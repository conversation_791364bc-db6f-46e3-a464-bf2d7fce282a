#!/bin/bash

# Script de redémarrage pour CRM Racha Groupe
# Usage: ./restart-server.sh

# Configuration
APP_DIR="/var/www/crm-racha-groupe"

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "🔄 Redémarrage du serveur CRM Racha Groupe..."

# Vérification que nous sommes dans le bon répertoire
if [ ! -f "$APP_DIR/stop-server.sh" ] || [ ! -f "$APP_DIR/start-server.sh" ]; then
    error "Scripts de gestion non trouvés dans $APP_DIR"
    error "Assurez-vous d'être dans le bon répertoire"
    exit 1
fi

# Arrêt du serveur
info "Étape 1/3: Arrêt du serveur..."
if bash "$APP_DIR/stop-server.sh"; then
    log "✅ Serveur arrêté"
else
    error "❌ Erreur lors de l'arrêt du serveur"
    exit 1
fi

# Attendre un peu pour s'assurer que le port est libéré
info "Étape 2/3: Attente de libération du port..."
sleep 3

# Vérification que le port 5001 est libre
if netstat -tlnp 2>/dev/null | grep :5001 > /dev/null; then
    error "Le port 5001 est encore utilisé"
    info "Processus utilisant le port 5001:"
    netstat -tlnp 2>/dev/null | grep :5001
    error "Arrêtez manuellement le processus ou attendez quelques secondes"
    exit 1
fi

# Démarrage du serveur
info "Étape 3/3: Démarrage du serveur..."
if bash "$APP_DIR/start-server.sh"; then
    log "✅ Redémarrage terminé avec succès"
    info ""
    info "🌐 Votre application est maintenant accessible"
    info "📊 Vérifiez le statut avec: ./server-status.sh"
    info "📋 Consultez les logs avec: tail -f logs/server.log"
else
    error "❌ Erreur lors du démarrage du serveur"
    error "Consultez les logs d'erreur: tail -f logs/error.log"
    exit 1
fi
