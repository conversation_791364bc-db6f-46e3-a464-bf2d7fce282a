import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as contactController from '../controllers/contact.controller';

const router = Router();

router.get('/', authenticateJWT, contactController.getAllContacts);
router.get('/:id', authenticateJWT, contactController.getContactById);
router.post('/', authenticateJWT, authorize<PERSON>ole(['admin', 'user']), contactController.createContact);
router.put('/:id', authenticateJWT, authorize<PERSON>ole(['admin', 'user']), contactController.updateContact);
router.delete('/:id', authenticateJWT, authorize<PERSON>ole(['admin']), contactController.deleteContact);

export default router; 