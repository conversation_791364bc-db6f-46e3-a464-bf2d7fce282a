# 📦 INSTALLATION MANUELLE - CRM Racha Groupe sur VPS OVH

## 🗂️ STRUCTURE DES FICHIERS À CHARGER

### **Dossier racine sur le serveur : `/var/www/crm-racha-groupe/`**

```
/var/www/crm-racha-groupe/
├── backend/                          # Dossier backend
│   ├── dist/                        # Backend compilé (à générer)
│   │   ├── app.js                   # Application principale
│   │   ├── controllers/             # Contrôleurs compilés
│   │   ├── services/               # Services compilés
│   │   ├── routes/                 # Routes compilées
│   │   ├── middlewares/            # Middlewares compilés
│   │   └── utils/                  # Utilitaires compilés
│   ├── src/                        # Code source backend
│   ├── package.json                # Dépendances backend
│   ├── package-lock.json           # Lock file backend
│   └── tsconfig.json               # Config TypeScript
├── dist/                           # Frontend compilé (à générer)
│   ├── index.html                  # Page principale
│   ├── assets/                     # Assets compilés
│   │   ├── *.js                    # Fichiers JavaScript
│   │   ├── *.css                   # Fichiers CSS
│   │   └── *.svg, *.png, etc.      # Images et icônes
│   └── favicon.ico                 # Favicon
├── database/                       # Scripts base de données
│   └── schema.sql                  # Schéma de la base
├── logs/                          # Dossier des logs (à créer)
├── uploads/                       # Dossier uploads (à créer)
├── .env                           # Variables d'environnement
├── start-server.sh                # Script de démarrage (alternative PM2)
├── stop-server.sh                 # Script d'arrêt
├── restart-server.sh              # Script de redémarrage
└── server-status.sh               # Script de vérification
```

## 🛠️ ÉTAPES D'INSTALLATION MANUELLE

### **1. Préparation du serveur**

```bash
# Connexion SSH à votre VPS
ssh root@votre-ip-vps

# Mise à jour du système
apt update && apt upgrade -y

# Installation des dépendances
apt install -y curl wget git nginx mysql-server nodejs npm

# Vérification des versions
node --version  # Doit être 16+
npm --version
```

### **2. Configuration de la base de données**

```bash
# Sécurisation MySQL
mysql_secure_installation

# Création de la base de données
mysql -u root -p
```

```sql
CREATE DATABASE admin_crm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'kiwiland'@'localhost' IDENTIFIED BY '*H@dFcMq0q38nvrz';
GRANT ALL PRIVILEGES ON admin_crm.* TO 'kiwiland'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### **3. Création de la structure de dossiers**

```bash
# Création du dossier principal
mkdir -p /var/www/crm-racha-groupe
cd /var/www/crm-racha-groupe

# Création des dossiers nécessaires
mkdir -p logs uploads backend/dist dist
chown -R www-data:www-data /var/www/crm-racha-groupe
chmod -R 755 /var/www/crm-racha-groupe
```

### **4. Upload des fichiers**

**Méthode 1 : Via SCP/SFTP**
```bash
# Depuis votre machine locale, uploadez les fichiers :
scp -r ./backend/ root@votre-ip:/var/www/crm-racha-groupe/
scp -r ./database/ root@votre-ip:/var/www/crm-racha-groupe/
scp .env.production root@votre-ip:/var/www/crm-racha-groupe/.env
```

**Méthode 2 : Via Git (recommandé)**
```bash
# Sur le serveur
cd /var/www/crm-racha-groupe
git clone https://github.com/your-repo/crm-racha-groupe.git .
cp .env.production .env
```

### **5. Installation des dépendances**

```bash
# Dépendances frontend
npm install

# Dépendances backend
cd backend
npm install
cd ..
```

### **6. Compilation des applications**

```bash
# Build frontend
npm run build

# Build backend
cd backend
npm run build
cd ..
```

### **7. Import du schéma de base de données**

```bash
mysql -u kiwiland -p admin_crm < database/schema.sql
```

## 🚀 ALTERNATIVE À PM2 - SCRIPTS DE GESTION

### **Script de démarrage : `start-server.sh`**

```bash
#!/bin/bash

# Configuration
APP_DIR="/var/www/crm-racha-groupe"
PID_FILE="$APP_DIR/server.pid"
LOG_FILE="$APP_DIR/logs/server.log"
ERROR_LOG="$APP_DIR/logs/error.log"

# Fonction de log
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Vérifier si le serveur est déjà en cours
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        log "Le serveur est déjà en cours d'exécution (PID: $PID)"
        exit 1
    else
        log "Fichier PID obsolète trouvé, suppression..."
        rm -f "$PID_FILE"
    fi
fi

# Démarrage du serveur
log "Démarrage du serveur CRM Racha Groupe..."

cd "$APP_DIR/backend"
export NODE_ENV=production
export PORT=5001

# Démarrage en arrière-plan
nohup node dist/app.js >> "$LOG_FILE" 2>> "$ERROR_LOG" &
SERVER_PID=$!

# Sauvegarde du PID
echo $SERVER_PID > "$PID_FILE"

log "Serveur démarré avec le PID: $SERVER_PID"
log "Logs disponibles dans: $LOG_FILE"
log "Erreurs disponibles dans: $ERROR_LOG"
log "Application accessible sur: http://localhost:5001"

# Vérification que le serveur a bien démarré
sleep 3
if ps -p $SERVER_PID > /dev/null 2>&1; then
    log "✅ Serveur démarré avec succès"
else
    log "❌ Erreur lors du démarrage du serveur"
    rm -f "$PID_FILE"
    exit 1
fi
```

### **Script d'arrêt : `stop-server.sh`**

```bash
#!/bin/bash

APP_DIR="/var/www/crm-racha-groupe"
PID_FILE="$APP_DIR/server.pid"
LOG_FILE="$APP_DIR/logs/server.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

if [ ! -f "$PID_FILE" ]; then
    log "Aucun fichier PID trouvé. Le serveur n'est peut-être pas en cours d'exécution."
    exit 1
fi

PID=$(cat "$PID_FILE")

if ps -p $PID > /dev/null 2>&1; then
    log "Arrêt du serveur (PID: $PID)..."
    kill $PID
    
    # Attendre l'arrêt
    for i in {1..10}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            break
        fi
        sleep 1
    done
    
    # Forcer l'arrêt si nécessaire
    if ps -p $PID > /dev/null 2>&1; then
        log "Arrêt forcé du serveur..."
        kill -9 $PID
    fi
    
    rm -f "$PID_FILE"
    log "✅ Serveur arrêté avec succès"
else
    log "Le processus avec PID $PID n'existe pas"
    rm -f "$PID_FILE"
fi
```

### **Script de redémarrage : `restart-server.sh`**

```bash
#!/bin/bash

APP_DIR="/var/www/crm-racha-groupe"

echo "🔄 Redémarrage du serveur CRM Racha Groupe..."

# Arrêt du serveur
bash "$APP_DIR/stop-server.sh"

# Attendre un peu
sleep 2

# Démarrage du serveur
bash "$APP_DIR/start-server.sh"

echo "✅ Redémarrage terminé"
```

### **Script de statut : `server-status.sh`**

```bash
#!/bin/bash

APP_DIR="/var/www/crm-racha-groupe"
PID_FILE="$APP_DIR/server.pid"

if [ ! -f "$PID_FILE" ]; then
    echo "❌ Serveur arrêté (aucun fichier PID)"
    exit 1
fi

PID=$(cat "$PID_FILE")

if ps -p $PID > /dev/null 2>&1; then
    echo "✅ Serveur en cours d'exécution (PID: $PID)"
    echo "📊 Utilisation mémoire:"
    ps -p $PID -o pid,ppid,cmd,%mem,%cpu --no-headers
    echo "🌐 Test de connectivité:"
    curl -s http://localhost:5001/api/health || echo "❌ API non accessible"
else
    echo "❌ Serveur arrêté (processus PID $PID non trouvé)"
    rm -f "$PID_FILE"
    exit 1
fi
```

## 🔧 CONFIGURATION NGINX

### **Fichier : `/etc/nginx/sites-available/crm-racha`**

```nginx
server {
    listen 80;
    server_name crm.rachadigital.com www.crm.rachadigital.com;
    
    # Redirection HTTPS (optionnel)
    # return 301 https://$server_name$request_uri;
    
    root /var/www/crm-racha-groupe/dist;
    index index.html;
    
    # Logs
    access_log /var/log/nginx/crm-access.log;
    error_log /var/log/nginx/crm-error.log;
    
    # Compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # API Backend
    location /api/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Uploads
    location /uploads/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Frontend
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache pour les assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### **Activation de la configuration Nginx**

```bash
# Lien symbolique
ln -s /etc/nginx/sites-available/crm-racha /etc/nginx/sites-enabled/

# Test de la configuration
nginx -t

# Redémarrage de Nginx
systemctl restart nginx
systemctl enable nginx
```

## 🔄 DÉMARRAGE AUTOMATIQUE AU BOOT

### **Service systemd : `/etc/systemd/system/crm-racha.service`**

```ini
[Unit]
Description=CRM Racha Groupe
After=network.target mysql.service

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/var/www/crm-racha-groupe
ExecStart=/var/www/crm-racha-groupe/start-server.sh
ExecStop=/var/www/crm-racha-groupe/stop-server.sh
ExecReload=/var/www/crm-racha-groupe/restart-server.sh
PIDFile=/var/www/crm-racha-groupe/server.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### **Activation du service**

```bash
# Rechargement des services
systemctl daemon-reload

# Activation du service
systemctl enable crm-racha

# Démarrage du service
systemctl start crm-racha

# Vérification du statut
systemctl status crm-racha
```

## 📋 COMMANDES DE GESTION

```bash
# Démarrage manuel
bash /var/www/crm-racha-groupe/start-server.sh

# Arrêt manuel
bash /var/www/crm-racha-groupe/stop-server.sh

# Redémarrage manuel
bash /var/www/crm-racha-groupe/restart-server.sh

# Vérification du statut
bash /var/www/crm-racha-groupe/server-status.sh

# Via systemd
systemctl start crm-racha
systemctl stop crm-racha
systemctl restart crm-racha
systemctl status crm-racha

# Logs en temps réel
tail -f /var/www/crm-racha-groupe/logs/server.log
tail -f /var/www/crm-racha-groupe/logs/error.log
```

## ✅ CHECKLIST FINALE

- [ ] Serveur VPS préparé (Node.js, MySQL, Nginx installés)
- [ ] Base de données créée et configurée
- [ ] Fichiers uploadés dans `/var/www/crm-racha-groupe/`
- [ ] Dépendances installées (frontend + backend)
- [ ] Applications compilées (dist/ et backend/dist/)
- [ ] Fichier .env configuré
- [ ] Scripts de gestion créés et rendus exécutables
- [ ] Configuration Nginx activée
- [ ] Service systemd configuré (optionnel)
- [ ] Test de l'application : `curl http://localhost:5001/api/health`

Cette approche manuelle vous donne un contrôle total sur votre déploiement sans dépendre de PM2 ! 🚀
