import { Request, Response } from 'express';
import * as serviceService from '../services/service.service';

export async function getAllServices(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await serviceService.getAllServices(page, limit);
    res.json(result);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getServiceById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const service = await serviceService.getServiceById(id);
    if (!service) return res.status(404).json({ error: 'Service non trouvé' });
    res.json(service);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createService(req: Request, res: Response) {
  try {
    const id = await serviceService.createService(req.body);
    // Audit log (exemple)
    res.status(201).json({ message: 'Service créé', id });
  } catch (err: any) {
    res.status(400).json({ error: err.message });
  }
}

export async function updateService(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await serviceService.updateService(id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Service non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Service modifié' });
  } catch (err: any) {
    if (err.message === 'Validation failed') {
      res.status(400).json({ error: 'Validation', details: err });
    } else {
      res.status(500).json({ error: 'Erreur serveur' });
    }
  }
}

export async function deleteService(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await serviceService.deleteService(id);
    if (affected === 0) return res.status(404).json({ error: 'Service non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Service supprimé' });
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}