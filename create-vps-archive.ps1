# Script de création d'archive pour déploiement VPS
# Usage: .\create-vps-archive.ps1

param(
    [string]$OutputPath = "crm-racha-groupe-vps.zip",
    [switch]$IncludeNodeModules = $false
)

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Info($message) {
    Write-ColorOutput Blue "ℹ️  $message"
}

function Write-Header($message) {
    Write-ColorOutput Cyan "`n📦 $message"
    Write-ColorOutput Cyan ("=" * 50)
}

# En-tête
Write-ColorOutput Magenta @"
📦 CRÉATION ARCHIVE VPS - CRM Racha Groupe
==========================================
"@

try {
    Write-Header "Vérification des fichiers requis"
    
    # Vérifier que les builds existent
    if (-not (Test-Path "dist")) {
        Write-Error "Dossier dist/ manquant. Exécutez d'abord le build frontend"
        Write-Info "Commande: npm run build"
        exit 1
    }
    
    if (-not (Test-Path "backend/dist")) {
        Write-Error "Dossier backend/dist/ manquant. Exécutez d'abord le build backend"
        Write-Info "Commande: cd backend && npm run build"
        exit 1
    }
    
    Write-Success "Builds trouvés"
    
    # Fichiers et dossiers à inclure
    $filesToInclude = @(
        # Code source
        "backend/src",
        "src",
        "public",
        "database",
        
        # Builds
        "dist",
        "backend/dist",
        
        # Configuration
        "package.json",
        "package-lock.json",
        "backend/package.json",
        "backend/package-lock.json",
        "backend/tsconfig.json",
        "vite.config.ts",
        "tailwind.config.ts",
        "tsconfig.json",
        "tsconfig.app.json",
        "tsconfig.node.json",
        "postcss.config.js",
        "components.json",
        
        # Environnement
        ".env.production",
        
        # Scripts de gestion
        "start-server.sh",
        "stop-server.sh",
        "restart-server.sh",
        "server-status.sh",
        "deploy-manual.sh",
        "crm-racha.service",
        
        # Documentation
        "README.md",
        "INSTALLATION-MANUELLE-VPS.md",
        "STRUCTURE-FICHIERS-VPS.md",
        "PROBLEMES-IDENTIFIES.md",
        "DEPLOYMENT-GUIDE.md"
    )
    
    # Ajouter node_modules si demandé
    if ($IncludeNodeModules) {
        $filesToInclude += @("node_modules", "backend/node_modules")
        Write-Info "node_modules inclus dans l'archive"
    }
    
    Write-Header "Création de l'archive"
    
    # Supprimer l'archive existante si elle existe
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Force
        Write-Info "Archive existante supprimée"
    }
    
    # Créer un dossier temporaire
    $tempDir = "temp_vps_archive"
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    
    Write-Info "Copie des fichiers..."
    
    # Copier les fichiers
    $copiedFiles = 0
    $skippedFiles = 0
    
    foreach ($file in $filesToInclude) {
        if (Test-Path $file) {
            $destPath = Join-Path $tempDir $file
            $destDir = Split-Path $destPath -Parent
            
            # Créer le dossier de destination si nécessaire
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            
            # Copier le fichier ou dossier
            if (Test-Path $file -PathType Container) {
                Copy-Item $file $destPath -Recurse -Force
            } else {
                Copy-Item $file $destPath -Force
            }
            
            $copiedFiles++
            Write-Success "Copié: $file"
        } else {
            $skippedFiles++
            Write-Info "Ignoré (non trouvé): $file"
        }
    }
    
    Write-Info "Fichiers copiés: $copiedFiles"
    Write-Info "Fichiers ignorés: $skippedFiles"
    
    # Créer l'archive ZIP
    Write-Info "Compression en cours..."
    Compress-Archive -Path "$tempDir/*" -DestinationPath $OutputPath -Force
    
    # Nettoyer le dossier temporaire
    Remove-Item $tempDir -Recurse -Force
    
    # Informations sur l'archive
    $archiveInfo = Get-Item $OutputPath
    $archiveSize = [math]::Round($archiveInfo.Length / 1MB, 2)
    
    Write-Header "Archive créée avec succès!"
    
    Write-Success "📁 Fichier: $($archiveInfo.FullName)"
    Write-Success "📊 Taille: $archiveSize MB"
    Write-Success "📅 Créé: $($archiveInfo.CreationTime)"
    
    Write-Info ""
    Write-ColorOutput Green @"
🎉 ARCHIVE PRÊTE POUR LE VPS!
=============================

📤 Étapes de déploiement:

1. 📤 Transférez l'archive sur votre VPS:
   scp $OutputPath root@VOTRE-IP-VPS:/tmp/

2. 📂 Sur le VPS, extrayez l'archive:
   cd /var/www
   unzip /tmp/$OutputPath
   mv crm-racha-groupe /var/www/

3. 🚀 Lancez le déploiement:
   cd /var/www/crm-racha-groupe
   chmod +x deploy-manual.sh
   ./deploy-manual.sh

📖 Consultez aussi:
   - INSTALLATION-MANUELLE-VPS.md
   - STRUCTURE-FICHIERS-VPS.md
"@

}
catch {
    Write-Error "Erreur lors de la création de l'archive: $($_.Exception.Message)"
    
    # Nettoyer en cas d'erreur
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
    }
    
    exit 1
}
