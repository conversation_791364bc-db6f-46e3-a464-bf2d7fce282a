@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Transition pour le changement de thème */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
  transition: background-color 300ms ease-in-out, border-color 300ms ease-in-out, color 300ms ease-in-out !important;
  transition-delay: 0ms !important;
}

@layer base {
  :root {
    --background: 210 50% 98%;
    --foreground: 200 50% 10%;

    --card: 0 0% 100%;
    --card-foreground: 200 50% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 200 50% 10%;

    --primary: 177 74% 40%;
    --primary-foreground: 210 40% 98%;

    --secondary: 196 100% 50%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 200 50% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 178 75% 38%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 200 50% 10%;
    --sidebar-primary: 177 74% 40%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 200 50% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 177 74% 40%;
  }

  .dark {
    --background: 200 50% 10%;
    --foreground: 210 40% 98%;

    --card: 200 50% 10%;
    --card-foreground: 210 40% 98%;

    --popover: 200 50% 10%;
    --popover-foreground: 210 40% 98%;

    --primary: 178 75% 38%;
    --primary-foreground: 210 40% 98%;

    --secondary: 196 100% 50%;
    --secondary-foreground: 0 0% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 177 74% 40%;

    --sidebar-background: 200 50% 10%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 177 74% 40%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 25% 15%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 25% 15%;
    --sidebar-ring: 177 74% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

.card-dashboard {
  @apply rounded-lg border bg-card shadow-sm hover:shadow-md transition-all duration-200;
}

.dashboard-stats-card {
  @apply flex flex-col p-4 rounded-lg border bg-card shadow-sm;
}

.gradient-teal {
  @apply bg-gradient-to-r from-racha-teal to-racha-blue text-white;
}

/* Accessibilité */
:root {
  --font-size-base: 16px;
}

html {
  font-size: var(--font-size-base);
}

/* Mode contraste élevé */
.high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --primary: #ffff00;
  --secondary: #00ffff;
  
  /* Augmenter les contrastes */
  --border-color: #ffffff;
  --text-color: #ffffff;
  --link-color: #ffff00;
}

/* Réduction des animations */
.reduced-motion * {
  animation: none !important;
  transition: none !important;
}

/* Focus visible */
.focus-visible :focus {
  outline: 3px solid var(--primary) !important;
  outline-offset: 2px !important;
}

/* Support des lecteurs d'écran */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Support du mode sombre */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
  }
}
