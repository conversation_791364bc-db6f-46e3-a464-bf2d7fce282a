#!/bin/bash

# Script d'arrêt pour CRM Racha Groupe
# Usage: ./stop-server.sh

# Configuration
APP_DIR="/var/www/crm-racha-groupe"
PID_FILE="$APP_DIR/server.pid"
LOG_FILE="$APP_DIR/logs/server.log"

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}" | tee -a "$LOG_FILE"
}

# Vérification du fichier PID
if [ ! -f "$PID_FILE" ]; then
    warning "Aucun fichier PID trouvé ($PID_FILE)"
    warning "Le serveur n'est peut-être pas en cours d'exécution"
    
    # Recherche de processus Node.js qui pourraient être notre serveur
    NODEJS_PIDS=$(pgrep -f "node.*app.js")
    if [ ! -z "$NODEJS_PIDS" ]; then
        warning "Processus Node.js trouvés:"
        ps -p $NODEJS_PIDS -o pid,cmd --no-headers
        info "Si l'un de ces processus est votre serveur CRM, arrêtez-le manuellement:"
        info "kill <PID>"
    fi
    exit 1
fi

PID=$(cat "$PID_FILE")

# Vérification que le processus existe
if ps -p $PID > /dev/null 2>&1; then
    log "🛑 Arrêt du serveur CRM Racha Groupe (PID: $PID)..."
    
    # Arrêt gracieux
    kill $PID
    
    # Attendre l'arrêt (maximum 15 secondes)
    for i in {1..15}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            log "✅ Serveur arrêté gracieusement"
            rm -f "$PID_FILE"
            exit 0
        fi
        echo -n "."
        sleep 1
    done
    
    echo ""
    warning "Le serveur ne s'arrête pas gracieusement"
    
    # Arrêt forcé
    if ps -p $PID > /dev/null 2>&1; then
        warning "Arrêt forcé du serveur..."
        kill -9 $PID
        
        # Vérification finale
        sleep 2
        if ps -p $PID > /dev/null 2>&1; then
            error "❌ Impossible d'arrêter le serveur (PID: $PID)"
            error "Essayez manuellement: kill -9 $PID"
            exit 1
        else
            log "✅ Serveur arrêté de force"
        fi
    fi
    
    rm -f "$PID_FILE"
    log "Fichier PID supprimé"
    
else
    warning "Le processus avec PID $PID n'existe pas"
    warning "Suppression du fichier PID obsolète"
    rm -f "$PID_FILE"
fi

log "🔴 Serveur CRM Racha Groupe arrêté"
