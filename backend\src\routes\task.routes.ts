import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as taskController from '../controllers/task.controller';

const router = Router();

router.get('/', authenticateJWT, taskController.getAllTasks);
router.get('/:id', authenticateJWT, taskController.getTaskById);
router.post('/', authenticateJWT, authorizeRole(['admin', 'user']), taskController.createTask);
router.put('/:id', authenticateJWT, authorize<PERSON>ole(['admin', 'user']), taskController.updateTask);
router.delete('/:id', authenticateJWT, authorizeRole(['admin']), taskController.deleteTask);

export default router; 