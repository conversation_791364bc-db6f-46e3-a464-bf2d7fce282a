import { Request, Response } from 'express';
import { getAllFiles, getFileById, createFile, updateFile, deleteFile } from '../services/file.service';

export const getAll = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const files = await getAllFiles(page, limit);
    res.json(files);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des fichiers' });
  }
};

export const getById = async (req: Request, res: Response) => {
  try {
    const file = await getFileById(parseInt(req.params.id));
    if (!file) return res.status(404).json({ message: 'Fichier non trouvé' });
    res.json(file);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération du fichier' });
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const file = await createFile(req.body);
    res.status(201).json(file);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const update = async (req: Request, res: Response) => {
  try {
    const file = await updateFile(parseInt(req.params.id), req.body);
    if (!file) return res.status(404).json({ message: 'Fichier non trouvé' });
    res.json(file);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const remove = async (req: Request, res: Response) => {
  try {
    await deleteFile(parseInt(req.params.id));
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la suppression du fichier' });
  }
};