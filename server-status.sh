#!/bin/bash

# Script de vérification du statut pour CRM Racha Groupe
# Usage: ./server-status.sh

# Configuration
APP_DIR="/var/www/crm-racha-groupe"
PID_FILE="$APP_DIR/server.pid"

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}$1${NC}"
}

error() {
    echo -e "${RED}$1${NC}"
}

warning() {
    echo -e "${YELLOW}$1${NC}"
}

info() {
    echo -e "${BLUE}$1${NC}"
}

header() {
    echo -e "${CYAN}$1${NC}"
}

# En-tête
header "🔍 STATUT DU SERVEUR CRM RACHA GROUPE"
header "========================================"

# Vérification du fichier PID
if [ ! -f "$PID_FILE" ]; then
    error "❌ Serveur arrêté (aucun fichier PID trouvé)"
    echo ""
    info "Pour démarrer le serveur:"
    info "  ./start-server.sh"
    echo ""
    
    # Recherche de processus Node.js
    NODEJS_PIDS=$(pgrep -f "node.*app.js" 2>/dev/null)
    if [ ! -z "$NODEJS_PIDS" ]; then
        warning "⚠️  Processus Node.js détectés (possiblement orphelins):"
        ps -p $NODEJS_PIDS -o pid,ppid,cmd,%mem,%cpu --no-headers 2>/dev/null
    fi
    
    exit 1
fi

PID=$(cat "$PID_FILE")

# Vérification que le processus existe
if ps -p $PID > /dev/null 2>&1; then
    log "✅ Serveur en cours d'exécution"
    echo ""
    
    # Informations sur le processus
    header "📊 INFORMATIONS DU PROCESSUS"
    echo "PID: $PID"
    echo "Démarré: $(ps -p $PID -o lstart --no-headers 2>/dev/null)"
    echo "Temps d'exécution: $(ps -p $PID -o etime --no-headers 2>/dev/null)"
    echo ""
    
    # Utilisation des ressources
    header "💾 UTILISATION DES RESSOURCES"
    ps -p $PID -o pid,ppid,cmd,%mem,%cpu --no-headers 2>/dev/null
    echo ""
    
    # Test de connectivité API
    header "🌐 TEST DE CONNECTIVITÉ"
    
    # Test du port
    if netstat -tlnp 2>/dev/null | grep :5001 > /dev/null; then
        log "✅ Port 5001 en écoute"
    else
        error "❌ Port 5001 non accessible"
    fi
    
    # Test de l'API Health Check
    echo -n "Test API Health Check... "
    if curl -s --max-time 5 http://localhost:5001/api/health > /dev/null 2>&1; then
        log "✅ API accessible"
        
        # Détails de la réponse health check
        HEALTH_RESPONSE=$(curl -s --max-time 5 http://localhost:5001/api/health 2>/dev/null)
        if [ ! -z "$HEALTH_RESPONSE" ]; then
            echo "Réponse: $HEALTH_RESPONSE"
        fi
    else
        error "❌ API non accessible"
        warning "Vérifiez les logs d'erreur: tail -f logs/error.log"
    fi
    
    echo ""
    
    # Informations sur les logs
    header "📋 LOGS RÉCENTS"
    if [ -f "$APP_DIR/logs/server.log" ]; then
        echo "Dernières lignes du log serveur:"
        tail -n 5 "$APP_DIR/logs/server.log" 2>/dev/null | sed 's/^/  /'
    fi
    
    if [ -f "$APP_DIR/logs/error.log" ] && [ -s "$APP_DIR/logs/error.log" ]; then
        echo ""
        warning "Erreurs récentes détectées:"
        tail -n 3 "$APP_DIR/logs/error.log" 2>/dev/null | sed 's/^/  /'
    fi
    
    echo ""
    
    # URLs d'accès
    header "🔗 URLS D'ACCÈS"
    info "Application: http://votre-serveur:5001"
    info "API: http://votre-serveur:5001/api"
    info "Health Check: http://votre-serveur:5001/api/health"
    
    echo ""
    
    # Commandes utiles
    header "🛠️  COMMANDES UTILES"
    info "Arrêter: ./stop-server.sh"
    info "Redémarrer: ./restart-server.sh"
    info "Logs temps réel: tail -f logs/server.log"
    info "Logs d'erreur: tail -f logs/error.log"
    
else
    error "❌ Serveur arrêté (processus PID $PID non trouvé)"
    warning "Suppression du fichier PID obsolète..."
    rm -f "$PID_FILE"
    echo ""
    info "Pour démarrer le serveur:"
    info "  ./start-server.sh"
    exit 1
fi
