#!/bin/bash

# Script de démarrage rapide pour test local
# Usage: ./quick-start.sh

set -e

echo "🚀 Démarrage rapide CRM Racha Groupe"
echo "===================================="

# Couleurs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# Vérification rapide
log "Vérification de l'environnement..."

if [ ! -f ".env" ]; then
    warning "Fichier .env manquant, copie depuis .env.production"
    cp .env.production .env
fi

# Installation rapide des dépendances si nécessaire
if [ ! -d "node_modules" ]; then
    log "Installation des dépendances frontend..."
    npm install
fi

if [ ! -d "backend/node_modules" ]; then
    log "Installation des dépendances backend..."
    cd backend && npm install && cd ..
fi

# Build rapide
log "Build de l'application..."
npm run build:all

# Diagnostic de santé
log "Diagnostic de santé..."
node health-check.js

# Démarrage
log "Démarrage de l'application..."
info "Backend sur: http://localhost:5001"
info "API sur: http://localhost:5001/api"
info "Health check: http://localhost:5001/api/health"
info ""
info "Appuyez sur Ctrl+C pour arrêter"

# Démarrage du backend
cd backend
NODE_ENV=production node dist/app.js
