import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import path from 'path';
import compression from 'compression';
import winston from 'winston';
import expressWinston from 'express-winston';

// Chargement des variables d'environnement
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Fallback pour les variables d'environnement critiques
if (!process.env.DB_HOST) {
  console.error('❌ Variable DB_HOST manquante dans .env');
  process.exit(1);
}

if (!process.env.DB_PASSWORD) {
  console.error('❌ Variable DB_PASSWORD manquante dans .env');
  process.exit(1);
}

if (!process.env.JWT_SECRET) {
  console.error('❌ Variable JWT_SECRET manquante dans .env');
  process.exit(1);
}

import { connectDB } from './utils/db';
import { errorHandler } from './middlewares/errorHandler';

// Import des routes principales
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import contactRoutes from './routes/contact.routes';
import quoteRoutes from './routes/quote.routes';
import invoiceRoutes from './routes/invoice.routes';
import productRoutes from './routes/product.routes';
import serviceRoutes from './routes/service.routes';
import taskRoutes from './routes/task.routes';
import eventRoutes from './routes/event.routes';
import settingsRoutes from './routes/settings.routes';
import integrationRoutes from './routes/integration.routes';
import fileRoutes from './routes/file.routes';

const app = express();

// Log du mode environnement
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('Serving static from:', path.join(__dirname, '../../dist'));

// Middlewares globaux
const allowedOrigins = [
  process.env.FRONTEND_URL,
  'https://crm.rachadigital.com',
  'http://localhost:3000',
  'http://localhost:8080'
].filter(Boolean);

app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? allowedOrigins : '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'"],
      imgSrc: ["'self'"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
}));

app.use(compression({
  level: 6,
  threshold: 100 * 1024
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(morgan('dev'));
app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100
}));

// Dossier pour les fichiers uploadés
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Routes API
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/contacts', contactRoutes);
app.use('/api/quotes', quoteRoutes);
app.use('/api/invoices', invoiceRoutes);
app.use('/api/products', productRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/events', eventRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/integrations', integrationRoutes);
app.use('/api/files', fileRoutes);

// Endpoint de santé
app.get('/api/health', async (req, res) => {
  try {
    const connection = await connectDB().getConnection();
    connection.release();
    res.status(200).json({ status: 'ok', db: 'connected' });
  } catch (e) {
    res.status(500).json({ status: 'error', db: 'disconnected' });
  }
});

// Servir les fichiers statiques du frontend en production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../../dist')));

  // Gérer les routes SPA
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../../dist', 'index.html'));
  });
}

// Gestion des erreurs
app.use(errorHandler);

// Configuration du logger
const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

// Middlewares globaux
app.use(compression()); // Ajout de la compression
app.use(expressWinston.logger({
  winstonInstance: logger,
  meta: true,
  msg: 'HTTP {{req.method}} {{req.url}}',
  expressFormat: true
}));

const PORT = process.env.PORT || 5001;

// Test de connexion à la base de données au démarrage
async function startServer() {
  try {
    console.log('🔍 Test de connexion à la base de données...');
    const connection = await connectDB().getConnection();
    connection.release();
    console.log('✅ Connexion à la base de données réussie');

    app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 Backend CRM lancé sur le port ${PORT}`);
      console.log(`📊 Environnement: ${process.env.NODE_ENV}`);
      console.log(`🌐 API disponible sur: http://localhost:${PORT}/api`);
      console.log(`❤️  Health check: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    console.error('❌ Erreur de connexion à la base de données:', error);
    console.error('🔧 Vérifiez votre configuration .env');
    process.exit(1);
  }
}

startServer();