# Script de build optimisé pour Windows - CRM Racha Groupe
# Usage: .\build-simple.ps1

param(
    [switch]$Clean = $false,
    [switch]$SkipValidation = $false
)

Write-Host "🚀 BUILD CRM RACHA GROUPE - Version Optimisée" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Nettoyage préalable si demandé
if ($Clean) {
    Write-Host "`n🧹 Nettoyage des anciens builds..." -ForegroundColor Blue
    if (Test-Path "dist") { Remove-Item "dist" -Recurse -Force }
    if (Test-Path "backend/dist") { Remove-Item "backend/dist" -Recurse -Force }
    Write-Host "Nettoyage terminé" -ForegroundColor Green
}

# Verification Node.js
Write-Host "`n🔍 Vérification de l'environnement..." -ForegroundColor Blue
try {
    $nodeVersion = node --version
    $majorVersion = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')

    if ($majorVersion -ge 18) {
        Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Version Node.js trop ancienne. Requis: 18+, Actuel: $majorVersion"
    }
} catch {
    Write-Host "❌ Erreur: Node.js non trouvé ou version incompatible" -ForegroundColor Red
    Write-Host "Installez Node.js 18+ depuis https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Verification npm
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "Erreur: npm non trouve" -ForegroundColor Red
    exit 1
}

# Verification fichiers requis
Write-Host "`nVerification des fichiers..." -ForegroundColor Blue
$requiredFiles = @("package.json", "backend/package.json", "backend/src/app.ts")

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "Fichier trouve: $file" -ForegroundColor Green
    } else {
        Write-Host "Fichier manquant: $file" -ForegroundColor Red
        exit 1
    }
}

# Verification .env
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.production") {
        Copy-Item ".env.production" ".env"
        Write-Host "Fichier .env cree depuis .env.production" -ForegroundColor Yellow
    } else {
        Write-Host "Erreur: Fichier .env manquant" -ForegroundColor Red
        Write-Host "Copiez .env.production vers .env et configurez-le" -ForegroundColor Yellow
        exit 1
    }
}

# Build backend
Write-Host "`nCompilation du backend..." -ForegroundColor Blue
Push-Location backend
try {
    npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Erreur compilation backend"
    }
    
    if (Test-Path "dist/app.js") {
        Write-Host "Backend compile avec succes" -ForegroundColor Green
    } else {
        throw "Fichier de sortie backend non trouve"
    }
} catch {
    Write-Host "Erreur lors de la compilation du backend: $_" -ForegroundColor Red
    Pop-Location
    exit 1
} finally {
    Pop-Location
}

# Build frontend
Write-Host "`nCompilation du frontend..." -ForegroundColor Blue
try {
    npx vite build
    if ($LASTEXITCODE -ne 0) {
        throw "Erreur compilation frontend"
    }
    
    if (Test-Path "dist/index.html") {
        Write-Host "Frontend compile avec succes" -ForegroundColor Green
    } else {
        throw "Fichier de sortie frontend non trouve"
    }
} catch {
    Write-Host "Erreur lors de la compilation du frontend: $_" -ForegroundColor Red
    exit 1
}

# Resume final
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "BUILD TERMINE AVEC SUCCES!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`nFichiers generes:" -ForegroundColor Blue
Write-Host "- dist/ (frontend compile)" -ForegroundColor White
Write-Host "- backend/dist/ (backend compile)" -ForegroundColor White

# Taille des builds
if (Test-Path "dist") {
    $frontendSize = (Get-ChildItem -Path "dist" -Recurse | Measure-Object -Property Length -Sum).Sum
    $frontendSizeMB = [math]::Round($frontendSize/1MB, 2)
    Write-Host "- Frontend: $frontendSizeMB MB" -ForegroundColor White
}

if (Test-Path "backend/dist") {
    $backendSize = (Get-ChildItem -Path "backend/dist" -Recurse | Measure-Object -Property Length -Sum).Sum
    $backendSizeMB = [math]::Round($backendSize/1MB, 2)
    Write-Host "- Backend: $backendSizeMB MB" -ForegroundColor White
}

Write-Host "`nProchaines etapes:" -ForegroundColor Blue
Write-Host "1. Creer une archive: .\create-vps-archive.ps1" -ForegroundColor White
Write-Host "2. Transferer sur votre VPS OVH" -ForegroundColor White
Write-Host "3. Suivre le guide: INSTALLATION-MANUELLE-VPS.md" -ForegroundColor White
Write-Host "4. Executer: ./deploy-manual.sh" -ForegroundColor White

Write-Host "`nGuides disponibles:" -ForegroundColor Blue
Write-Host "- GUIDE-WINDOWS-VPS.md" -ForegroundColor White
Write-Host "- INSTALLATION-MANUELLE-VPS.md" -ForegroundColor White
Write-Host "- STRUCTURE-FICHIERS-VPS.md" -ForegroundColor White
