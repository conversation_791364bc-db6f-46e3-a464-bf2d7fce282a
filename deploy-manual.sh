#!/bin/bash

# Script de déploiement manuel pour VPS OVH
# Alternative complète à PM2
# Usage: ./deploy-manual.sh

set -e

# Configuration
APP_DIR="/var/www/crm-racha-groupe"
NGINX_SITE="crm-racha"

# Couleurs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

header() {
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo $1 | sed 's/./=/g')${NC}"
}

# Vérification des droits root
if [ "$EUID" -ne 0 ]; then
    error "Ce script doit être exécuté en tant que root (sudo)"
fi

header "🚀 DÉPLOIEMENT MANUEL CRM RACHA GROUPE"

# Étape 1: Vérification du système
log "Étape 1/8: Vérification du système..."

# Vérifier Ubuntu/Debian
if ! command -v apt &> /dev/null; then
    error "Ce script est conçu pour Ubuntu/Debian (apt requis)"
fi

# Mise à jour du système
log "Mise à jour du système..."
apt update && apt upgrade -y

# Étape 2: Installation des dépendances
log "Étape 2/8: Installation des dépendances..."

# Node.js 18+
if ! command -v node &> /dev/null || [ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 18 ]; then
    log "Installation de Node.js 18..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt install -y nodejs
fi

# Autres dépendances
apt install -y git nginx mysql-server curl wget

log "✅ Dépendances installées"
info "Node.js version: $(node --version)"
info "npm version: $(npm --version)"

# Étape 3: Configuration MySQL
log "Étape 3/8: Configuration de la base de données..."

# Démarrage de MySQL
systemctl start mysql
systemctl enable mysql

# Vérification si la base existe déjà
if mysql -u root -e "USE admin_crm;" 2>/dev/null; then
    warning "Base de données admin_crm existe déjà"
else
    log "Création de la base de données..."
    mysql -u root -e "
    CREATE DATABASE admin_crm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    CREATE USER IF NOT EXISTS 'kiwiland'@'localhost' IDENTIFIED BY '*H@dFcMq0q38nvrz';
    GRANT ALL PRIVILEGES ON admin_crm.* TO 'kiwiland'@'localhost';
    FLUSH PRIVILEGES;
    "
    log "✅ Base de données créée"
fi

# Étape 4: Préparation des dossiers
log "Étape 4/8: Préparation des dossiers..."

# Création du dossier principal
mkdir -p "$APP_DIR"
mkdir -p "$APP_DIR/logs"
mkdir -p "$APP_DIR/uploads"

# Permissions
chown -R www-data:www-data "$APP_DIR"
chmod -R 755 "$APP_DIR"

log "✅ Dossiers préparés"

# Étape 5: Copie des fichiers (si pas déjà fait)
log "Étape 5/8: Vérification des fichiers..."

if [ ! -f "$APP_DIR/package.json" ]; then
    warning "Fichiers de l'application non trouvés dans $APP_DIR"
    info "Copiez vos fichiers dans $APP_DIR ou clonez le repository:"
    info "  cd $APP_DIR"
    info "  git clone https://github.com/your-repo/crm-racha-groupe.git ."
    info "Puis relancez ce script"
    exit 1
fi

# Étape 6: Installation et compilation
log "Étape 6/8: Installation et compilation..."

cd "$APP_DIR"

# Configuration .env
if [ ! -f ".env" ]; then
    if [ -f ".env.production" ]; then
        cp .env.production .env
        log "Fichier .env créé depuis .env.production"
    else
        error "Fichier .env manquant. Créez-le avant de continuer"
    fi
fi

# Installation des dépendances
log "Installation des dépendances frontend..."
sudo -u www-data npm install

log "Installation des dépendances backend..."
cd backend
sudo -u www-data npm install
cd ..

# Compilation
log "Compilation du frontend..."
sudo -u www-data npm run build

log "Compilation du backend..."
cd backend
sudo -u www-data npm run build
cd ..

# Import du schéma si nécessaire
if [ -f "database/schema.sql" ]; then
    log "Import du schéma de base de données..."
    mysql -u kiwiland -p*H@dFcMq0q38nvrz admin_crm < database/schema.sql
fi

log "✅ Applications compilées"

# Étape 7: Configuration des scripts de gestion
log "Étape 7/8: Configuration des scripts de gestion..."

# Rendre les scripts exécutables
chmod +x start-server.sh stop-server.sh restart-server.sh server-status.sh

# Configuration du service systemd
if [ -f "crm-racha.service" ]; then
    cp crm-racha.service /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable crm-racha
    log "✅ Service systemd configuré"
fi

# Étape 8: Configuration Nginx
log "Étape 8/8: Configuration Nginx..."

# Configuration Nginx
cat > /etc/nginx/sites-available/$NGINX_SITE << 'EOF'
server {
    listen 80;
    server_name _;
    
    root /var/www/crm-racha-groupe/dist;
    index index.html;
    
    # Logs
    access_log /var/log/nginx/crm-access.log;
    error_log /var/log/nginx/crm-error.log;
    
    # Compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # API Backend
    location /api/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Uploads
    location /uploads/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Frontend
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Cache pour les assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# Activation du site
ln -sf /etc/nginx/sites-available/$NGINX_SITE /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test et redémarrage Nginx
nginx -t && systemctl restart nginx && systemctl enable nginx

log "✅ Nginx configuré"

# Démarrage de l'application
log "Démarrage de l'application..."
cd "$APP_DIR"
sudo -u www-data ./start-server.sh

# Résumé final
header "🎉 DÉPLOIEMENT TERMINÉ AVEC SUCCÈS!"

info "📁 Application installée dans: $APP_DIR"
info "🌐 Application accessible sur: http://$(hostname -I | awk '{print $1}')"
info "📊 API Health Check: http://$(hostname -I | awk '{print $1}')/api/health"

echo ""
header "🛠️  COMMANDES DE GESTION"
info "Démarrer: cd $APP_DIR && ./start-server.sh"
info "Arrêter: cd $APP_DIR && ./stop-server.sh"
info "Redémarrer: cd $APP_DIR && ./restart-server.sh"
info "Statut: cd $APP_DIR && ./server-status.sh"

echo ""
header "🔧 GESTION VIA SYSTEMD"
info "Démarrer: systemctl start crm-racha"
info "Arrêter: systemctl stop crm-racha"
info "Redémarrer: systemctl restart crm-racha"
info "Statut: systemctl status crm-racha"
info "Logs: journalctl -u crm-racha -f"

echo ""
header "📋 PROCHAINES ÉTAPES"
info "1. Configurez votre nom de domaine dans Nginx"
info "2. Installez un certificat SSL avec Let's Encrypt"
info "3. Configurez les sauvegardes de base de données"
info "4. Mettez en place la surveillance"

echo ""
warning "⚠️  N'oubliez pas de:"
warning "- Sécuriser votre serveur (firewall, fail2ban)"
warning "- Changer les mots de passe par défaut"
warning "- Configurer les sauvegardes automatiques"

log "🚀 Votre CRM Racha Groupe est maintenant opérationnel!"
