import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';

const db = connectDB();

const productSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  category: z.string().optional(),
  price: z.number().min(0),
  sku: z.string().optional(),
  stock: z.number().min(0).optional(),
  status: z.string().optional()
});

export async function getAllProducts(page: number = 1, limit: number = 10) {
  return paginateQuery('products', page, limit);
}

export async function getProductById(id: string) {
  const [rows] = await db.query('SELECT * FROM products WHERE id = ?', [id]);
  return (rows as any[])[0];
}

export async function createProduct(data: any) {
  const parse = productSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { name, description, category, price, sku, stock, status } = parse.data;
  const [result] = await db.query('INSERT INTO products (name, description, category, price, sku, stock, status) VALUES (?, ?, ?, ?, ?, ?, ?)', [name, description, category, price, sku, stock, status]);
  return (result as any).insertId;
}

export async function updateProduct(id: string, data: any) {
  const parse = productSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields = { ...parse.data };
  const [result] = await db.query('UPDATE products SET ? WHERE id = ?', [fields, id]);
  return (result as any).affectedRows;
}

export async function deleteProduct(id: string) {
  const [result] = await db.query('DELETE FROM products WHERE id = ?', [id]);
  return (result as any).affectedRows;
}