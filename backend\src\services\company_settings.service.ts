import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const settingSchema = z.object({
  setting_key: z.string().min(1, 'Clé requise'),
  setting_value: z.object({}).optional(), // JSON
  description: z.string().optional(),
  updated_by: z.number().optional(),
});

export const getAllSettings = async (page: number = 1, limit: number = 10) => {
  return paginateQuery('company_settings', page, limit);
};

export const getSettingByKey = async (key: string) => {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM company_settings WHERE setting_key = ?', [key]);
  return rows[0] || null;
};

export const createSetting = async (data: z.infer<typeof settingSchema>) => {
  const validatedData = settingSchema.parse(data);
  const db = connectDB();
  const [result] = await db.query<OkPacket>(
    'INSERT INTO company_settings (setting_key, setting_value, description, updated_by) VALUES (?, ?, ?, ?)',
    [
      validatedData.setting_key,
      JSON.stringify(validatedData.setting_value),
      validatedData.description,
      validatedData.updated_by,
    ]
  );
  return { id: result.insertId, ...validatedData };
};

export const updateSetting = async (key: string, data: Partial<z.infer<typeof settingSchema>>) => {
  const validatedData = settingSchema.partial().parse(data);
  const db = connectDB();
  const updates = [];
  const values = [];
  for (const [k, value] of Object.entries(validatedData)) {
    if (value !== undefined && k !== 'setting_key') {
      updates.push(`${k} = ?`);
      values.push(k === 'setting_value' ? JSON.stringify(value) : value);
    }
  }
  if (updates.length === 0) return null;
  values.push(key);
  await db.query<OkPacket>(`UPDATE company_settings SET ${updates.join(', ')} WHERE setting_key = ?`, values);
  return getSettingByKey(key);
};

export const deleteSetting = async (key: string) => {
  const db = connectDB();
  await db.query<OkPacket>('DELETE FROM company_settings WHERE setting_key = ?', [key]);
  return { message: 'Paramètre supprimé avec succès' };
};