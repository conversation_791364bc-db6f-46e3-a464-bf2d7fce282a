import { z } from 'zod';
import { connectDB } from '../utils/db';
import { paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const bonLivraisonSchema = z.object({
  bl_number: z.string().min(1, 'Numéro BL requis'),
  invoice_id: z.number().optional(),
  quote_id: z.number().optional(),
  contact_id: z.number().optional(),
  company_id: z.number().optional(),
  client_name: z.string().min(1),
  client_address: z.string().optional(),
  client_phone: z.string().optional(),
  client_email: z.string().email().optional(),
  delivery_date: z.date(),
  delivery_address: z.string().optional(),
  delivery_contact: z.string().optional(),
  delivery_phone: z.string().optional(),
  driver_name: z.string().optional(),
  transporter: z.string().optional(),
  vehicle: z.string().optional(),
  delivery_mode: z.enum(['livraison_directe', 'transporteur', 'retrait_client', 'coursier']).optional(),
  status: z.enum(['en_preparation', 'expedie', 'en_cours_livraison', 'livre', 'partiellement_livre', 'refuse', 'retour']).optional(),
  total_packages: z.number().optional(),
  total_weight: z.number().optional(),
  total_volume: z.number().optional(),
  delivery_conditions: z.string().optional(),
  general_notes: z.string().optional(),
  client_notes: z.string().optional(),
  client_signature: z.boolean().optional(),
  driver_signature: z.boolean().optional(),
  created_by: z.number().optional(),
});

export const getAllBonLivraisons = async (page: number = 1, limit: number = 10) => {
  return paginateQuery('bon_livraison', page, limit);
};

export const getBonLivraisonById = async (id: number) => {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM bon_livraison WHERE id = ?', [id]);
  return rows[0] || null;
};

export const createBonLivraison = async (data: z.infer<typeof bonLivraisonSchema>) => {
  const validatedData = bonLivraisonSchema.parse(data);
  const db = connectDB();
  const [result] = await db.query<OkPacket>(
    'INSERT INTO bon_livraison (bl_number, invoice_id, quote_id, contact_id, company_id, client_name, client_address, client_phone, client_email, delivery_date, delivery_address, delivery_contact, delivery_phone, driver_name, transporter, vehicle, delivery_mode, status, total_packages, total_weight, total_volume, delivery_conditions, general_notes, client_notes, client_signature, driver_signature, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [
      validatedData.bl_number,
      validatedData.invoice_id,
      validatedData.quote_id,
      validatedData.contact_id,
      validatedData.company_id,
      validatedData.client_name,
      validatedData.client_address,
      validatedData.client_phone,
      validatedData.client_email,
      validatedData.delivery_date,
      validatedData.delivery_address,
      validatedData.delivery_contact,
      validatedData.delivery_phone,
      validatedData.driver_name,
      validatedData.transporter,
      validatedData.vehicle,
      validatedData.delivery_mode,
      validatedData.status,
      validatedData.total_packages,
      validatedData.total_weight,
      validatedData.total_volume,
      validatedData.delivery_conditions,
      validatedData.general_notes,
      validatedData.client_notes,
      validatedData.client_signature,
      validatedData.driver_signature,
      validatedData.created_by,
    ]
  );
  return { id: result.insertId, ...validatedData };
};

export const updateBonLivraison = async (id: number, data: Partial<z.infer<typeof bonLivraisonSchema>>) => {
  const validatedData = bonLivraisonSchema.partial().parse(data);
  const db = connectDB();
  const updates = [];
  const values = [];
  for (const [key, value] of Object.entries(validatedData)) {
    if (value !== undefined) {
      updates.push(`${key} = ?`);
      values.push(value);
    }
  }
  if (updates.length === 0) return null;
  values.push(id);
  await db.query<OkPacket>(`UPDATE bon_livraison SET ${updates.join(', ')} WHERE id = ?`, values);
  return getBonLivraisonById(id);
};

export const deleteBonLivraison = async (id: number) => {
  const db = connectDB();
  await db.query<OkPacket>('DELETE FROM bon_livraison WHERE id = ?', [id]);
  return { message: 'Bon de livraison supprimé avec succès' };
};