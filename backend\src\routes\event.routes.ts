import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as eventController from '../controllers/event.controller';

const router = Router();

router.get('/', authenticateJWT, eventController.getAllEvents);
router.get('/:id', authenticateJWT, eventController.getEventById);
router.post('/', authenticateJWT, authorizeRole(['admin', 'user']), eventController.createEvent);
router.put('/:id', authenticateJWT, authorizeRole(['admin', 'user']), eventController.updateEvent);
router.delete('/:id', authenticateJWT, authorizeRole(['admin']), eventController.deleteEvent);

export default router; 