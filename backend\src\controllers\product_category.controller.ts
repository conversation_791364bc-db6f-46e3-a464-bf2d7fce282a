import { Request, Response } from 'express';
import { getAllCategories, getCategoryById, createCategory, updateCategory, deleteCategory } from '../services/product_category.service';

export const getAll = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const categories = await getAllCategories(page, limit);
    res.json(categories);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des catégories' });
  }
};

export const getById = async (req: Request, res: Response) => {
  try {
    const category = await getCategoryById(parseInt(req.params.id));
    if (!category) return res.status(404).json({ message: 'Catégorie non trouvée' });
    res.json(category);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération de la catégorie' });
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const category = await createCategory(req.body);
    res.status(201).json(category);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const update = async (req: Request, res: Response) => {
  try {
    const category = await updateCategory(parseInt(req.params.id), req.body);
    if (!category) return res.status(404).json({ message: 'Catégorie non trouvée' });
    res.json(category);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const remove = async (req: Request, res: Response) => {
  try {
    await deleteCategory(parseInt(req.params.id));
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la suppression de la catégorie' });
  }
};