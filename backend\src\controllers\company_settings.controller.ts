import { Request, Response } from 'express';
import { getAllSettings, getSettingByKey, createSetting, updateSetting, deleteSetting } from '../services/company_settings.service';

export const getAll = async (req: Request, res: Response) => {
  try {
    const settings = await getAllSettings();
    res.json(settings);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des paramètres' });
  }
};

export const getByKey = async (req: Request, res: Response) => {
  try {
    const setting = await getSettingByKey(req.params.key);
    if (!setting) return res.status(404).json({ message: 'Paramètre non trouvé' });
    res.json(setting);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération du paramètre' });
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const setting = await createSetting(req.body);
    res.status(201).json(setting);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const update = async (req: Request, res: Response) => {
  try {
    const setting = await updateSetting(req.params.key, req.body);
    if (!setting) return res.status(404).json({ message: 'Paramètre non trouvé' });
    res.json(setting);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const remove = async (req: Request, res: Response) => {
  try {
    await deleteSetting(req.params.key);
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la suppression du paramètre' });
  }
};