#!/usr/bin/env node

/**
 * Script de vérification de santé pour CRM Racha Groupe
 * Usage: node health-check.js
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Couleurs pour les logs
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function header(message) {
  log(`\n🔍 ${message}`, 'cyan');
  log('='.repeat(50), 'cyan');
}

async function checkNodeVersion() {
  header('Vérification de Node.js');
  
  try {
    const version = process.version;
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    info(`Version Node.js: ${version}`);
    
    if (majorVersion >= 18) {
      success('Version Node.js compatible');
    } else {
      error(`Version Node.js trop ancienne. Requis: 18+, Actuel: ${majorVersion}`);
      return false;
    }
  } catch (err) {
    error(`Erreur lors de la vérification de Node.js: ${err.message}`);
    return false;
  }
  
  return true;
}

async function checkFiles() {
  header('Vérification des fichiers');
  
  const requiredFiles = [
    '.env',
    'package.json',
    'backend/package.json',
    'backend/src/app.ts',
    'database/schema.sql'
  ];
  
  let allFilesExist = true;
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      success(`Fichier trouvé: ${file}`);
    } else {
      error(`Fichier manquant: ${file}`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

async function checkEnvVariables() {
  header('Vérification des variables d\'environnement');
  
  if (!fs.existsSync('.env')) {
    error('Fichier .env manquant');
    return false;
  }
  
  const envContent = fs.readFileSync('.env', 'utf8');
  const requiredVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'JWT_SECRET',
    'PORT'
  ];
  
  let allVarsPresent = true;
  
  for (const varName of requiredVars) {
    if (envContent.includes(`${varName}=`)) {
      success(`Variable trouvée: ${varName}`);
    } else {
      error(`Variable manquante: ${varName}`);
      allVarsPresent = false;
    }
  }
  
  return allVarsPresent;
}

async function checkDependencies() {
  header('Vérification des dépendances');
  
  try {
    // Vérifier les dépendances frontend
    if (fs.existsSync('node_modules')) {
      success('Dépendances frontend installées');
    } else {
      warning('Dépendances frontend non installées');
      info('Exécutez: npm install');
    }
    
    // Vérifier les dépendances backend
    if (fs.existsSync('backend/node_modules')) {
      success('Dépendances backend installées');
    } else {
      warning('Dépendances backend non installées');
      info('Exécutez: cd backend && npm install');
    }
    
    return true;
  } catch (err) {
    error(`Erreur lors de la vérification des dépendances: ${err.message}`);
    return false;
  }
}

async function checkBuild() {
  header('Vérification des builds');
  
  let buildStatus = true;
  
  // Vérifier le build frontend
  if (fs.existsSync('dist')) {
    if (fs.existsSync('dist/index.html')) {
      success('Build frontend présent');
    } else {
      warning('Build frontend incomplet');
      buildStatus = false;
    }
  } else {
    warning('Build frontend manquant');
    info('Exécutez: npm run build');
    buildStatus = false;
  }
  
  // Vérifier le build backend
  if (fs.existsSync('backend/dist')) {
    if (fs.existsSync('backend/dist/app.js')) {
      success('Build backend présent');
    } else {
      warning('Build backend incomplet');
      buildStatus = false;
    }
  } else {
    warning('Build backend manquant');
    info('Exécutez: cd backend && npm run build');
    buildStatus = false;
  }
  
  return buildStatus;
}

async function checkDatabase() {
  header('Test de connexion à la base de données');

  try {
    // Charger les variables d'environnement
    const dotenv = await import('dotenv');
    dotenv.config();

    const mysql = await import('mysql2/promise');
    
    const connection = await mysql.default.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    await connection.execute('SELECT 1');
    await connection.end();
    
    success('Connexion à la base de données réussie');
    return true;
  } catch (err) {
    error(`Erreur de connexion à la base de données: ${err.message}`);
    info('Vérifiez vos paramètres de base de données dans .env');
    return false;
  }
}

async function checkPorts() {
  header('Vérification des ports');
  
  const ports = [5001]; // Port du backend
  
  for (const port of ports) {
    try {
      const result = execSync(`netstat -an | findstr :${port}`, { encoding: 'utf8' });
      
      if (result.trim()) {
        warning(`Port ${port} déjà utilisé`);
        info(result.trim());
      } else {
        success(`Port ${port} disponible`);
      }
    } catch (err) {
      success(`Port ${port} disponible`);
    }
  }
  
  return true;
}

async function generateReport() {
  header('Génération du rapport de santé');
  
  const checks = [
    { name: 'Node.js', fn: checkNodeVersion },
    { name: 'Fichiers', fn: checkFiles },
    { name: 'Variables d\'environnement', fn: checkEnvVariables },
    { name: 'Dépendances', fn: checkDependencies },
    { name: 'Builds', fn: checkBuild },
    { name: 'Base de données', fn: checkDatabase },
    { name: 'Ports', fn: checkPorts }
  ];
  
  const results = [];
  
  for (const check of checks) {
    try {
      const result = await check.fn();
      results.push({ name: check.name, status: result });
    } catch (err) {
      error(`Erreur lors de ${check.name}: ${err.message}`);
      results.push({ name: check.name, status: false });
    }
  }
  
  // Résumé
  header('Résumé du diagnostic');
  
  const passed = results.filter(r => r.status).length;
  const total = results.length;
  
  log(`\n📊 Résultats: ${passed}/${total} vérifications réussies\n`);
  
  results.forEach(result => {
    if (result.status) {
      success(result.name);
    } else {
      error(result.name);
    }
  });
  
  if (passed === total) {
    log('\n🎉 Toutes les vérifications sont passées! Votre application est prête.', 'green');
    log('\n🚀 Pour démarrer l\'application:', 'blue');
    log('   ./deploy-vps.sh', 'blue');
  } else {
    log('\n⚠️  Certaines vérifications ont échoué. Corrigez les problèmes avant de déployer.', 'yellow');
    log('\n📖 Consultez le guide de déploiement: DEPLOYMENT-GUIDE.md', 'blue');
  }
}

// Exécution du script
log('🏥 Diagnostic de santé - CRM Racha Groupe', 'magenta');
log('=' .repeat(50), 'magenta');

generateReport().catch(err => {
  error(`Erreur fatale: ${err.message}`);
  process.exit(1);
});
