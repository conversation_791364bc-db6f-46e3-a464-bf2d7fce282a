import { createPool, Pool, RowDataPacket } from 'mysql2/promise';

let pool: Pool;

export function connectDB(): Pool {
  if (pool) {
    return pool;
  }
  try {
    pool = createPool({
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT || 3306),
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      charset: 'utf8mb4_general_ci'
    });
    console.log('Database pool created.');
    return pool;
  } catch (error) {
    console.error('Database connection error:', error);
    throw error;
  }
}

// Fonction d'aide pour les requêtes paginées
export async function paginateQuery(table: string, page: number = 1, limit: number = 10) {
  const db = connectDB();
  const offset = (page - 1) * limit;
  const [dataRows] = await db.query<RowDataPacket[]>(`SELECT * FROM ${table} LIMIT ? OFFSET ?`, [limit, offset]);
  const [countRows] = await db.query<RowDataPacket[]>(`SELECT COUNT(*) as total FROM ${table}`);
  const total = countRows[0].total;
  return { data: dataRows, total, page, limit };
}