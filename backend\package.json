{"name": "crm-backend", "version": "1.0.0", "description": "Backend API pour CRM Racha Groupe (Express + TypeScript)", "main": "dist/app.js", "scripts": {"dev": "npx ts-node-dev --respawn --transpile-only src/app.ts", "build": "npx tsc", "start": "node dist/app.js"}, "author": "Racha Business Group", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.0.0", "express-winston": "^4.2.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mariadb": "^3.3.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.7", "winston": "^3.17.0", "zod": "^3.25.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/express-rate-limit": "^5.1.3", "@types/express-winston": "^3.0.4", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.7", "@types/node": "^22.5.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.3"}}