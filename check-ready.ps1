# Verification rapide avant deploiement
Write-Host "Verification du projet CRM Racha Groupe..." -ForegroundColor Cyan

$errors = 0

# Fichiers essentiels
$files = @(
    "package.json",
    "backend/package.json", 
    "backend/src/app.ts",
    "src/main.tsx",
    "database/schema.sql",
    ".env.production"
)

Write-Host "`nFichiers essentiels:" -ForegroundColor Blue
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file MANQUANT" -ForegroundColor Red
        $errors++
    }
}

# Scripts VPS
$scripts = @(
    "start-server.sh",
    "stop-server.sh",
    "restart-server.sh", 
    "server-status.sh",
    "deploy-manual.sh"
)

Write-Host "`nScripts VPS:" -ForegroundColor Blue
foreach ($script in $scripts) {
    if (Test-Path $script) {
        Write-Host "✅ $script" -ForegroundColor Green
    } else {
        Write-Host "❌ $script MANQUANT" -ForegroundColor Red
        $errors++
    }
}

# Resultat
Write-Host "`nResultat:" -ForegroundColor Yellow
if ($errors -eq 0) {
    Write-Host "✅ PROJET PRET POUR LE DEPLOIEMENT!" -ForegroundColor Green
    Write-Host "`nProchaines etapes:" -ForegroundColor Blue
    Write-Host "1. .\create-vps-archive.ps1" -ForegroundColor White
    Write-Host "2. Transferer sur VPS" -ForegroundColor White
    Write-Host "3. Executer deploy-manual.sh" -ForegroundColor White
} else {
    Write-Host "❌ $errors erreurs detectees" -ForegroundColor Red
    Write-Host "Corrigez les fichiers manquants avant de continuer" -ForegroundColor Red
}
