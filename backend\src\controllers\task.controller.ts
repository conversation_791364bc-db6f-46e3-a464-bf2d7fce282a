import { Request, Response } from 'express';
import * as taskService from '../services/task.service';

export async function getAllTasks(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await taskService.getAllTasks(page, limit);
    res.json(result);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getTaskById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const task = await taskService.getTaskById(id);
    if (!task) return res.status(404).json({ error: 'Tâche non trouvée' });
    res.json(task);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createTask(req: Request, res: Response) {
  try {
    const id = await taskService.createTask(req.body);
    // Audit log (exemple)
    res.status(201).json({ message: 'Tâche créée', id });
  } catch (err: any) {
    res.status(400).json({ error: err.message });
  }
}

export async function updateTask(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await taskService.updateTask(id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Tâche non trouvée' });
    // Audit log (exemple)
    res.json({ message: 'Tâche modifiée' });
  } catch (err: any) {
    if (err.message === 'Validation failed') {
      res.status(400).json({ error: 'Validation', details: err });
    } else {
      res.status(500).json({ error: 'Erreur serveur' });
    }
  }
}

export async function deleteTask(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await taskService.deleteTask(id);
    if (affected === 0) return res.status(404).json({ error: 'Tâche non trouvée' });
    // Audit log (exemple)
    res.json({ message: 'Tâche supprimée' });
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}