import { Request, Response } from 'express';
import * as eventService from '../services/event.service';

export async function getAllEvents(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await eventService.getAllEvents(page, limit);
    res.json(result);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getEventById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const event = await eventService.getEventById(id);
    if (!event) return res.status(404).json({ error: 'Événement non trouvé' });
    res.json(event);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createEvent(req: Request, res: Response) {
  try {
    const id = await eventService.createEvent(req.body);
    // Audit log (exemple)
    res.status(201).json({ message: 'Événement créé', id });
  } catch (err: any) {
    res.status(400).json({ error: err.message });
  }
}

export async function updateEvent(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await eventService.updateEvent(id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Événement non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Événement modifié' });
  } catch (err: any) {
    if (err.message === 'Validation failed') {
      res.status(400).json({ error: 'Validation', details: err });
    } else {
      res.status(500).json({ error: 'Erreur serveur' });
    }
  }
}

export async function deleteEvent(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await eventService.deleteEvent(id);
    if (affected === 0) return res.status(404).json({ error: 'Événement non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Événement supprimé' });
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}