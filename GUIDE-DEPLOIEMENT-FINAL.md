# 🚀 GUIDE DE DÉPLOIEMENT FINAL - CRM Racha Groupe sur VPS OVH

## 🎯 DÉPLOIEMENT 100% FONCTIONNEL EN 4 ÉTAPES

### **📋 PRÉREQUIS VALIDÉS**

✅ **Projet optimisé et nettoyé**  
✅ **Scripts de gestion VPS créés**  
✅ **Alternative complète à PM2**  
✅ **Configuration sécurisée**  
✅ **Documentation complète**  

---

## 🔧 **ÉTAPE 1: PRÉPARATION SUR WINDOWS**

### **1.1 Validation du projet**
```powershell
# Valider que tout est prêt
.\validate-deployment.ps1

# Nettoyage et optimisation
.\clean-project.ps1
```

### **1.2 Build complet**
```powershell
# Build optimisé avec nettoyage
.\build-simple.ps1 -Clean

# Vérifier les builds générés
ls dist/
ls backend/dist/
```

### **1.3 Création de l'archive VPS**
```powershell
# Créer l'archive optimisée
.\create-vps-archive.ps1

# Archive générée: crm-racha-groupe-vps.zip
```

---

## 📤 **ÉTAPE 2: TRANSFERT VERS VPS**

### **2.1 Via SCP (ligne de commande)**
```powershell
# Transférer l'archive
scp crm-racha-groupe-vps.zip root@VOTRE-IP-VPS:/tmp/
```

### **2.2 Via WinSCP (interface graphique)**
1. **Connexion** : IP VPS, utilisateur `root`, mot de passe
2. **Navigation** : Aller dans `/tmp/`
3. **Upload** : Glisser-déposer `crm-racha-groupe-vps.zip`

### **2.3 Via FileZilla SFTP**
1. **Protocole** : SFTP
2. **Hôte** : IP de votre VPS
3. **Utilisateur** : root
4. **Destination** : `/tmp/`

---

## 🖥️ **ÉTAPE 3: INSTALLATION SUR VPS**

### **3.1 Connexion SSH**
```bash
ssh root@VOTRE-IP-VPS
```

### **3.2 Extraction et positionnement**
```bash
# Aller dans le répertoire web
cd /var/www

# Extraire l'archive
unzip /tmp/crm-racha-groupe-vps.zip

# Renommer si nécessaire
mv crm-racha-groupe /var/www/crm-racha-groupe

# Aller dans le dossier
cd /var/www/crm-racha-groupe
```

### **3.3 Déploiement automatique**
```bash
# Rendre le script exécutable
chmod +x deploy-manual.sh

# Lancer le déploiement complet
./deploy-manual.sh
```

**Le script `deploy-manual.sh` fait automatiquement :**
- ✅ Installation Node.js, MySQL, Nginx
- ✅ Configuration de la base de données
- ✅ Installation des dépendances
- ✅ Compilation des applications
- ✅ Configuration Nginx avec reverse proxy
- ✅ Configuration du service systemd
- ✅ Démarrage de l'application

---

## ✅ **ÉTAPE 4: VÉRIFICATION ET TESTS**

### **4.1 Vérification du statut**
```bash
# Statut de l'application
./server-status.sh

# Statut via systemd
systemctl status crm-racha

# Test API
curl http://localhost:5001/api/health
```

### **4.2 Tests d'accès**
- **Application** : `http://VOTRE-IP-VPS`
- **API** : `http://VOTRE-IP-VPS/api`
- **Health Check** : `http://VOTRE-IP-VPS/api/health`

### **4.3 Vérification des logs**
```bash
# Logs application
tail -f logs/server.log

# Logs erreurs
tail -f logs/error.log

# Logs Nginx
tail -f /var/log/nginx/crm-access.log
```

---

## 🛠️ **GESTION QUOTIDIENNE**

### **Commandes de gestion**
```bash
# Démarrage
./start-server.sh

# Arrêt
./stop-server.sh

# Redémarrage
./restart-server.sh

# Statut détaillé
./server-status.sh
```

### **Gestion via systemd**
```bash
# Démarrage automatique
systemctl enable crm-racha
systemctl start crm-racha

# Contrôle
systemctl stop crm-racha
systemctl restart crm-racha
systemctl status crm-racha
```

---

## 🔒 **SÉCURISATION (OPTIONNEL)**

### **SSL avec Let's Encrypt**
```bash
# Installation Certbot
apt install certbot python3-certbot-nginx

# Obtention certificat SSL
certbot --nginx -d votre-domaine.com

# Test renouvellement
certbot renew --dry-run
```

### **Firewall UFW**
```bash
# Activation firewall
ufw enable
ufw allow ssh
ufw allow 80
ufw allow 443
ufw status
```

---

## 📊 **MONITORING ET MAINTENANCE**

### **Surveillance recommandée**
- **CPU/RAM** : `htop` ou `top`
- **Espace disque** : `df -h`
- **Logs** : `tail -f logs/*.log`
- **Processus** : `./server-status.sh`

### **Sauvegarde base de données**
```bash
# Sauvegarde manuelle
mysqldump -u kiwiland -p admin_crm > backup_$(date +%Y%m%d).sql

# Sauvegarde automatique (crontab)
0 2 * * * mysqldump -u kiwiland -p*H@dFcMq0q38nvrz admin_crm > /var/backups/crm_$(date +\%Y\%m\%d).sql
```

---

## 🆘 **DÉPANNAGE RAPIDE**

### **Application ne démarre pas**
```bash
# Vérifier les logs
tail -f logs/error.log

# Vérifier la base de données
mysql -u kiwiland -p admin_crm

# Redémarrer
./restart-server.sh
```

### **API non accessible**
```bash
# Vérifier le port
netstat -tlnp | grep :5001

# Vérifier Nginx
nginx -t
systemctl restart nginx
```

### **Erreur de permissions**
```bash
# Corriger les permissions
chown -R www-data:www-data /var/www/crm-racha-groupe
chmod +x *.sh
```

---

## 🎉 **RÉSULTAT FINAL**

Après ces 4 étapes, vous aurez :

✅ **CRM 100% fonctionnel** sur votre VPS OVH  
✅ **Interface web** accessible via votre IP  
✅ **API REST** complète et sécurisée  
✅ **Base de données** MySQL configurée  
✅ **Gestion automatique** des processus  
✅ **Logs structurés** pour le monitoring  
✅ **Scripts de maintenance** inclus  
✅ **Démarrage automatique** au boot  

### **URLs d'accès :**
- **Application** : `http://VOTRE-IP-VPS`
- **API** : `http://VOTRE-IP-VPS/api`
- **Health Check** : `http://VOTRE-IP-VPS/api/health`

### **Fonctionnalités CRM disponibles :**
- 👥 **Gestion des clients**
- 📄 **Devis et factures**
- 📊 **Tableau de bord**
- 🔐 **Authentification sécurisée**
- 📈 **Rapports et statistiques**
- 📱 **Interface responsive**

---

## 📞 **SUPPORT**

En cas de problème :

1. **Consultez les logs** : `tail -f logs/error.log`
2. **Vérifiez le statut** : `./server-status.sh`
3. **Redémarrez si nécessaire** : `./restart-server.sh`
4. **Consultez la documentation** : `STRUCTURE-FICHIERS-VPS.md`

**Votre CRM Racha Groupe est maintenant opérationnel à 100% ! 🚀**
