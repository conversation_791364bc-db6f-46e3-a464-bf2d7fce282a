# 📁 STRUCTURE DES FICHIERS POUR VPS OVH

## 🎯 FICHIERS À TÉLÉCHARGER/COPIER SUR VOTRE SERVEUR

### **📦 MÉTHODE 1: Archive ZIP complète**

Créez une archive avec cette structure exacte :

```
crm-racha-groupe.zip
├── backend/
│   ├── src/                        # Code source backend
│   │   ├── app.ts
│   │   ├── controllers/
│   │   ├── services/
│   │   ├── routes/
│   │   ├── middlewares/
│   │   └── utils/
│   ├── package.json                # Dépendances backend
│   ├── package-lock.json
│   └── tsconfig.json
├── src/                            # Code source frontend
│   ├── App.tsx
│   ├── main.tsx
│   ├── components/
│   ├── pages/
│   ├── contexts/
│   ├── services/
│   ├── types/
│   └── utils/
├── public/                         # Assets publics
│   ├── favicon.ico
│   ├── racha-digital-logo.svg
│   └── images/
├── database/
│   └── schema.sql                  # Schéma de base de données
├── package.json                    # Dépendances frontend
├── package-lock.json
├── vite.config.ts
├── tailwind.config.ts
├── tsconfig.json
├── .env.production                 # Variables d'environnement
├── start-server.sh                 # Scripts de gestion
├── stop-server.sh
├── restart-server.sh
├── server-status.sh
├── deploy-manual.sh                # Script de déploiement
├── crm-racha.service              # Service systemd
└── README.md
```

### **📋 MÉTHODE 2: Liste des fichiers essentiels**

**Fichiers obligatoires à copier :**

1. **Code source complet**
   - `backend/` (dossier entier)
   - `src/` (dossier entier)
   - `public/` (dossier entier)
   - `database/schema.sql`

2. **Configuration**
   - `package.json` (racine)
   - `package-lock.json` (racine)
   - `backend/package.json`
   - `backend/package-lock.json`
   - `vite.config.ts`
   - `tailwind.config.ts`
   - `tsconfig.json`
   - `backend/tsconfig.json`

3. **Environnement**
   - `.env.production` → à renommer en `.env`

4. **Scripts de gestion** (nouveaux fichiers créés)
   - `start-server.sh`
   - `stop-server.sh`
   - `restart-server.sh`
   - `server-status.sh`
   - `deploy-manual.sh`
   - `crm-racha.service`

## 🚀 COMMANDES DE TRANSFERT

### **Via SCP (depuis votre machine locale)**

```bash
# Créer l'archive
tar -czf crm-racha-groupe.tar.gz \
  backend/ src/ public/ database/ \
  package.json package-lock.json \
  vite.config.ts tailwind.config.ts tsconfig.json \
  .env.production \
  start-server.sh stop-server.sh restart-server.sh server-status.sh \
  deploy-manual.sh crm-racha.service

# Transférer vers le serveur
scp crm-racha-groupe.tar.gz root@VOTRE-IP-VPS:/tmp/

# Sur le serveur, extraire
ssh root@VOTRE-IP-VPS
cd /var/www
tar -xzf /tmp/crm-racha-groupe.tar.gz
mv crm-racha-groupe /var/www/
```

### **Via SFTP/FTP**

1. Connectez-vous à votre VPS via SFTP
2. Naviguez vers `/var/www/`
3. Créez le dossier `crm-racha-groupe`
4. Uploadez tous les fichiers dans ce dossier

### **Via Git (recommandé)**

```bash
# Sur le serveur
cd /var/www
git clone https://github.com/your-username/crm-racha-groupe.git
cd crm-racha-groupe

# Copier les nouveaux scripts de gestion
# (si pas dans le repo Git)
```

## 🔧 APRÈS LE TRANSFERT

### **1. Permissions**
```bash
cd /var/www/crm-racha-groupe
chown -R www-data:www-data .
chmod -R 755 .
chmod +x *.sh
```

### **2. Configuration**
```bash
# Copier la configuration de production
cp .env.production .env

# Éditer avec vos paramètres
nano .env
```

### **3. Déploiement automatique**
```bash
# Rendre le script exécutable
chmod +x deploy-manual.sh

# Lancer le déploiement
./deploy-manual.sh
```

## 📊 VÉRIFICATION DE LA STRUCTURE

### **Script de vérification**

```bash
#!/bin/bash
# Vérifier que tous les fichiers sont présents

APP_DIR="/var/www/crm-racha-groupe"
cd "$APP_DIR"

echo "🔍 Vérification de la structure des fichiers..."

# Fichiers essentiels
FILES=(
    "package.json"
    "backend/package.json"
    "backend/src/app.ts"
    "database/schema.sql"
    ".env"
    "start-server.sh"
    "stop-server.sh"
    "restart-server.sh"
    "server-status.sh"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file MANQUANT"
    fi
done

# Dossiers essentiels
DIRS=(
    "backend/src"
    "src"
    "public"
    "database"
)

for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/"
    else
        echo "❌ $dir/ MANQUANT"
    fi
done

echo ""
echo "📋 Taille des dossiers:"
du -sh backend/ src/ public/ 2>/dev/null

echo ""
echo "🔧 Permissions:"
ls -la *.sh 2>/dev/null
```

## 🎯 CHECKLIST FINALE

- [ ] **Tous les fichiers source copiés**
  - [ ] `backend/` complet
  - [ ] `src/` complet  
  - [ ] `public/` complet
  - [ ] `database/schema.sql`

- [ ] **Configuration copiée**
  - [ ] `package.json` (racine et backend)
  - [ ] `vite.config.ts`
  - [ ] `tsconfig.json`
  - [ ] `.env` (depuis .env.production)

- [ ] **Scripts de gestion copiés**
  - [ ] `start-server.sh`
  - [ ] `stop-server.sh`
  - [ ] `restart-server.sh`
  - [ ] `server-status.sh`
  - [ ] `deploy-manual.sh`

- [ ] **Permissions configurées**
  - [ ] `chown -R www-data:www-data /var/www/crm-racha-groupe`
  - [ ] `chmod +x *.sh`

- [ ] **Prêt pour le déploiement**
  - [ ] `./deploy-manual.sh`

## 🆘 DÉPANNAGE

### **Fichiers manquants**
```bash
# Vérifier la structure
find /var/www/crm-racha-groupe -name "*.json" -o -name "*.sh" -o -name "*.sql"
```

### **Problèmes de permissions**
```bash
# Corriger toutes les permissions
cd /var/www/crm-racha-groupe
chown -R www-data:www-data .
chmod -R 755 .
chmod +x *.sh
```

### **Vérification de l'intégrité**
```bash
# Vérifier que les fichiers ne sont pas corrompus
file backend/src/app.ts
file package.json
```

Cette structure vous garantit un déploiement réussi sans PM2 ! 🚀
