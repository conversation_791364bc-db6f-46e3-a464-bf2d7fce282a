# 📁 STRUCTURE OPTIMISÉE POUR VPS OVH - CRM Racha Groupe

## 🎯 STRUCTURE FINALE OPTIMISÉE (APRÈS NETTOYAGE)

### **📦 ARCHIVE PRODUCTION COMPLÈTE**

Structure exacte à déployer sur votre VPS :

```
/var/www/crm-racha-groupe/
├── backend/                        # 🔧 BACKEND (Node.js + Express)
│   ├── src/                        # Code source TypeScript
│   │   ├── app.ts                  # Application principale
│   │   ├── controllers/            # Contrôleurs API
│   │   │   ├── authController.ts
│   │   │   ├── clientController.ts
│   │   │   ├── devisController.ts
│   │   │   ├── factureController.ts
│   │   │   ├── projetController.ts
│   │   │   └── userController.ts
│   │   ├── middlewares/            # Middlewares Express
│   │   │   ├── auth.ts
│   │   │   ├── upload.ts
│   │   │   └── validation.ts
│   │   ├── routes/                 # Routes API
│   │   │   ├── auth.ts
│   │   │   ├── clients.ts
│   │   │   ├── devis.ts
│   │   │   ├── factures.ts
│   │   │   ├── projets.ts
│   │   │   └── users.ts
│   │   ├── services/               # Services métier
│   │   │   ├── authService.ts
│   │   │   ├── clientService.ts
│   │   │   ├── devisService.ts
│   │   │   ├── factureService.ts
│   │   │   └── projetService.ts
│   │   └── utils/                  # Utilitaires
│   │       ├── db.ts               # Connexion base de données
│   │       ├── logger.ts           # Système de logs
│   │       └── validators.ts       # Validateurs Zod
│   ├── dist/                       # 📦 BACKEND COMPILÉ (généré)
│   │   ├── app.js                  # Application compilée
│   │   ├── controllers/            # Contrôleurs compilés
│   │   ├── middlewares/            # Middlewares compilés
│   │   ├── routes/                 # Routes compilées
│   │   ├── services/               # Services compilés
│   │   └── utils/                  # Utilitaires compilés
│   ├── package.json                # Dépendances backend
│   ├── package-lock.json           # Lock file backend
│   └── tsconfig.json               # Config TypeScript backend
├── src/                            # 🎨 FRONTEND (React + TypeScript)
│   ├── App.tsx                     # Composant principal
│   ├── main.tsx                    # Point d'entrée
│   ├── index.css                   # Styles globaux
│   ├── components/                 # Composants UI
│   │   ├── ui/                     # Composants de base (shadcn/ui)
│   │   ├── layout/                 # Layout et navigation
│   │   ├── forms/                  # Formulaires
│   │   └── charts/                 # Graphiques et tableaux
│   ├── pages/                      # Pages de l'application
│   │   ├── auth/                   # Pages d'authentification
│   │   ├── dashboard/              # Tableau de bord
│   │   ├── clients/                # Gestion clients
│   │   ├── devis/                  # Gestion devis
│   │   ├── factures/               # Gestion factures
│   │   └── projets/                # Gestion projets
│   ├── contexts/                   # Contextes React
│   │   ├── AuthContext.tsx
│   │   └── ThemeContext.tsx
│   ├── services/                   # Services API
│   │   ├── api.ts                  # Client API
│   │   ├── authService.ts
│   │   ├── clientService.ts
│   │   ├── devisService.ts
│   │   ├── factureService.ts
│   │   └── projetService.ts
│   ├── types/                      # Types TypeScript
│   │   ├── auth.ts
│   │   ├── client.ts
│   │   ├── devis.ts
│   │   ├── facture.ts
│   │   └── projet.ts
│   └── utils/                      # Utilitaires frontend
│       ├── constants.ts
│       ├── formatters.ts
│       └── validators.ts
├── dist/                           # 📦 FRONTEND COMPILÉ (généré)
│   ├── index.html                  # Page principale
│   ├── assets/                     # Assets optimisés
│   │   ├── index-[hash].js         # JavaScript compilé
│   │   ├── index-[hash].css        # CSS compilé
│   │   ├── vendor-[hash].js        # Librairies tierces
│   │   └── *.svg, *.png, *.ico     # Images optimisées
│   └── favicon.ico                 # Favicon
├── public/                         # 🖼️ ASSETS STATIQUES
│   ├── favicon.ico                 # Favicon principal
│   ├── racha-digital-logo.svg      # Logo principal
│   ├── racha-digital-icon.svg      # Icône
│   ├── racha-digital-logo-compact.svg # Logo compact
│   ├── images/                     # Images statiques
│   └── robots.txt                  # SEO
├── database/                       # 🗄️ BASE DE DONNÉES
│   ├── schema.sql                  # Schéma complet MySQL
│   └── README.md                   # Documentation DB
├── logs/                           # 📋 LOGS (créé automatiquement)
├── uploads/                        # 📁 FICHIERS UPLOADÉS (créé automatiquement)
├── package.json                    # 📦 Dépendances frontend
├── package-lock.json               # Lock file frontend
├── vite.config.ts                  # Configuration Vite
├── tailwind.config.ts              # Configuration Tailwind
├── postcss.config.js               # Configuration PostCSS
├── tsconfig.json                   # Config TypeScript principal
├── tsconfig.app.json               # Config TypeScript app
├── tsconfig.node.json              # Config TypeScript Node
├── components.json                 # Config shadcn/ui
├── eslint.config.js                # Configuration ESLint
├── .env                            # 🔐 Variables d'environnement (depuis .env.production)
├── start-server.sh                 # 🚀 Script de démarrage
├── stop-server.sh                  # 🛑 Script d'arrêt
├── restart-server.sh               # 🔄 Script de redémarrage
├── server-status.sh                # 📊 Script de statut
├── deploy-manual.sh                # 🚀 Script de déploiement complet
├── crm-racha.service              # ⚙️ Service systemd
└── README.md                       # 📖 Documentation
```

### **📋 MÉTHODE 2: Liste des fichiers essentiels**

**Fichiers obligatoires à copier :**

1. **Code source complet**
   - `backend/` (dossier entier)
   - `src/` (dossier entier)
   - `public/` (dossier entier)
   - `database/schema.sql`

2. **Configuration**
   - `package.json` (racine)
   - `package-lock.json` (racine)
   - `backend/package.json`
   - `backend/package-lock.json`
   - `vite.config.ts`
   - `tailwind.config.ts`
   - `tsconfig.json`
   - `backend/tsconfig.json`

3. **Environnement**
   - `.env.production` → à renommer en `.env`

4. **Scripts de gestion** (nouveaux fichiers créés)
   - `start-server.sh`
   - `stop-server.sh`
   - `restart-server.sh`
   - `server-status.sh`
   - `deploy-manual.sh`
   - `crm-racha.service`

## 🚀 COMMANDES DE TRANSFERT

### **Via SCP (depuis votre machine locale)**

```bash
# Créer l'archive
tar -czf crm-racha-groupe.tar.gz \
  backend/ src/ public/ database/ \
  package.json package-lock.json \
  vite.config.ts tailwind.config.ts tsconfig.json \
  .env.production \
  start-server.sh stop-server.sh restart-server.sh server-status.sh \
  deploy-manual.sh crm-racha.service

# Transférer vers le serveur
scp crm-racha-groupe.tar.gz root@VOTRE-IP-VPS:/tmp/

# Sur le serveur, extraire
ssh root@VOTRE-IP-VPS
cd /var/www
tar -xzf /tmp/crm-racha-groupe.tar.gz
mv crm-racha-groupe /var/www/
```

### **Via SFTP/FTP**

1. Connectez-vous à votre VPS via SFTP
2. Naviguez vers `/var/www/`
3. Créez le dossier `crm-racha-groupe`
4. Uploadez tous les fichiers dans ce dossier

### **Via Git (recommandé)**

```bash
# Sur le serveur
cd /var/www
git clone https://github.com/your-username/crm-racha-groupe.git
cd crm-racha-groupe

# Copier les nouveaux scripts de gestion
# (si pas dans le repo Git)
```

## 🔧 APRÈS LE TRANSFERT

### **1. Permissions**
```bash
cd /var/www/crm-racha-groupe
chown -R www-data:www-data .
chmod -R 755 .
chmod +x *.sh
```

### **2. Configuration**
```bash
# Copier la configuration de production
cp .env.production .env

# Éditer avec vos paramètres
nano .env
```

### **3. Déploiement automatique**
```bash
# Rendre le script exécutable
chmod +x deploy-manual.sh

# Lancer le déploiement
./deploy-manual.sh
```

## 📊 VÉRIFICATION DE LA STRUCTURE

### **Script de vérification**

```bash
#!/bin/bash
# Vérifier que tous les fichiers sont présents

APP_DIR="/var/www/crm-racha-groupe"
cd "$APP_DIR"

echo "🔍 Vérification de la structure des fichiers..."

# Fichiers essentiels
FILES=(
    "package.json"
    "backend/package.json"
    "backend/src/app.ts"
    "database/schema.sql"
    ".env"
    "start-server.sh"
    "stop-server.sh"
    "restart-server.sh"
    "server-status.sh"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file MANQUANT"
    fi
done

# Dossiers essentiels
DIRS=(
    "backend/src"
    "src"
    "public"
    "database"
)

for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/"
    else
        echo "❌ $dir/ MANQUANT"
    fi
done

echo ""
echo "📋 Taille des dossiers:"
du -sh backend/ src/ public/ 2>/dev/null

echo ""
echo "🔧 Permissions:"
ls -la *.sh 2>/dev/null
```

## 🎯 CHECKLIST FINALE

- [ ] **Tous les fichiers source copiés**
  - [ ] `backend/` complet
  - [ ] `src/` complet  
  - [ ] `public/` complet
  - [ ] `database/schema.sql`

- [ ] **Configuration copiée**
  - [ ] `package.json` (racine et backend)
  - [ ] `vite.config.ts`
  - [ ] `tsconfig.json`
  - [ ] `.env` (depuis .env.production)

- [ ] **Scripts de gestion copiés**
  - [ ] `start-server.sh`
  - [ ] `stop-server.sh`
  - [ ] `restart-server.sh`
  - [ ] `server-status.sh`
  - [ ] `deploy-manual.sh`

- [ ] **Permissions configurées**
  - [ ] `chown -R www-data:www-data /var/www/crm-racha-groupe`
  - [ ] `chmod +x *.sh`

- [ ] **Prêt pour le déploiement**
  - [ ] `./deploy-manual.sh`

## 🆘 DÉPANNAGE

### **Fichiers manquants**
```bash
# Vérifier la structure
find /var/www/crm-racha-groupe -name "*.json" -o -name "*.sh" -o -name "*.sql"
```

### **Problèmes de permissions**
```bash
# Corriger toutes les permissions
cd /var/www/crm-racha-groupe
chown -R www-data:www-data .
chmod -R 755 .
chmod +x *.sh
```

### **Vérification de l'intégrité**
```bash
# Vérifier que les fichiers ne sont pas corrompus
file backend/src/app.ts
file package.json
```

Cette structure vous garantit un déploiement réussi sans PM2 ! 🚀
