# 🚀 Guide de Déploiement - CRM Racha Groupe sur VPS OVH

## 📋 Prérequis

### Serveur VPS OVH
- **OS**: Ubuntu 20.04+ ou CentOS 8+
- **RAM**: Minimum 2GB (4GB recommandé)
- **Stockage**: Minimum 20GB
- **CPU**: 2 vCPU minimum

### Logiciels requis
- Node.js 18+
- npm ou yarn
- MySQL/MariaDB 10.3+
- Nginx
- PM2 (gestionnaire de processus)
- Git

## 🛠️ Installation étape par étape

### 1. Préparation du serveur

```bash
# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation des dépendances système
sudo apt install -y curl wget git nginx mysql-server

# Installation de Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Vérification des versions
node --version  # Doit être 18+
npm --version
```

### 2. Configuration de la base de données

```bash
# Sécurisation de MySQL
sudo mysql_secure_installation

# Connexion à MySQL
sudo mysql -u root -p

# Création de la base de données et utilisateur
CREATE DATABASE admin_crm CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'kiwiland'@'localhost' IDENTIFIED BY '*H@dFcMq0q38nvrz';
GRANT ALL PRIVILEGES ON admin_crm.* TO 'kiwiland'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Import du schéma
mysql -u kiwiland -p admin_crm < database/schema.sql
```

### 3. Déploiement de l'application

```bash
# Création du répertoire de déploiement
sudo mkdir -p /var/www/crm-racha-groupe
sudo chown $USER:$USER /var/www/crm-racha-groupe

# Clonage du repository
cd /var/www/crm-racha-groupe
git clone https://github.com/your-username/crm-racha-groupe.git .

# Configuration de l'environnement
cp .env.production .env
# Éditez le fichier .env avec vos paramètres
nano .env

# Installation de PM2 globalement
sudo npm install -g pm2

# Déploiement automatique
chmod +x deploy-vps.sh
./deploy-vps.sh
```

### 4. Configuration de PM2

```bash
# Démarrage avec PM2
pm2 start ecosystem.config.js --env production

# Configuration du démarrage automatique
pm2 startup
pm2 save

# Vérification du statut
pm2 status
pm2 logs crm-racha-backend
```

### 5. Configuration de Nginx

```bash
# Copie de la configuration
sudo cp nginx-crm.conf /etc/nginx/sites-available/crm-racha
sudo ln -s /etc/nginx/sites-available/crm-racha /etc/nginx/sites-enabled/

# Test de la configuration
sudo nginx -t

# Redémarrage de Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 6. Configuration SSL avec Let's Encrypt

```bash
# Installation de Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtention du certificat SSL
sudo certbot --nginx -d crm.rachadigital.com -d www.crm.rachadigital.com

# Test du renouvellement automatique
sudo certbot renew --dry-run
```

## 🔧 Configuration avancée

### Monitoring avec PM2

```bash
# Installation de PM2 Plus (optionnel)
pm2 install pm2-server-monit

# Monitoring en temps réel
pm2 monit
```

### Logs et surveillance

```bash
# Création du dossier de logs
sudo mkdir -p /var/log/crm-racha
sudo chown $USER:$USER /var/log/crm-racha

# Rotation des logs
sudo nano /etc/logrotate.d/crm-racha
```

### Sauvegarde automatique

```bash
# Script de sauvegarde de la base de données
sudo nano /usr/local/bin/backup-crm.sh

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u kiwiland -p*H@dFcMq0q38nvrz admin_crm > /var/backups/crm_backup_$DATE.sql
find /var/backups -name "crm_backup_*.sql" -mtime +30 -delete

# Rendre exécutable
sudo chmod +x /usr/local/bin/backup-crm.sh

# Ajouter au crontab
sudo crontab -e
0 2 * * * /usr/local/bin/backup-crm.sh
```

## 🚨 Dépannage

### Problèmes courants

1. **Erreur de connexion à la base de données**
   ```bash
   # Vérifier le statut de MySQL
   sudo systemctl status mysql
   
   # Tester la connexion
   mysql -u kiwiland -p admin_crm
   ```

2. **Application inaccessible**
   ```bash
   # Vérifier PM2
   pm2 status
   pm2 logs crm-racha-backend
   
   # Vérifier Nginx
   sudo nginx -t
   sudo systemctl status nginx
   ```

3. **Erreurs de permissions**
   ```bash
   # Corriger les permissions
   sudo chown -R $USER:$USER /var/www/crm-racha-groupe
   sudo chmod -R 755 /var/www/crm-racha-groupe
   ```

### Commandes utiles

```bash
# Redémarrer l'application
pm2 restart crm-racha-backend

# Voir les logs en temps réel
pm2 logs crm-racha-backend --lines 100

# Mettre à jour l'application
cd /var/www/crm-racha-groupe
git pull origin main
npm run build:all
pm2 restart crm-racha-backend

# Vérifier la santé de l'API
curl http://localhost:5001/api/health
```

## 📊 Monitoring et maintenance

### Surveillance recommandée
- Utilisation CPU/RAM
- Espace disque
- Logs d'erreur
- Temps de réponse API
- Connexions base de données

### Maintenance régulière
- Mise à jour des dépendances
- Sauvegarde de la base de données
- Rotation des logs
- Surveillance de la sécurité

## 🔒 Sécurité

### Recommandations
- Changez tous les mots de passe par défaut
- Configurez un firewall (UFW)
- Activez fail2ban
- Mettez à jour régulièrement le système
- Surveillez les logs d'accès

### Configuration firewall

```bash
# Installation et configuration UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw status
```

## 📞 Support

En cas de problème, vérifiez :
1. Les logs PM2 : `pm2 logs`
2. Les logs Nginx : `sudo tail -f /var/log/nginx/error.log`
3. Les logs système : `sudo journalctl -u nginx`
4. L'état des services : `sudo systemctl status nginx mysql`
