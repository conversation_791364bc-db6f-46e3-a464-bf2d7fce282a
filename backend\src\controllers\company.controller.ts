import { Request, Response } from 'express';
import { getAllCompanies, getCompanyById, createCompany, updateCompany, deleteCompany } from '../services/company.service';

export const getAll = async (req: Request, res: Response) => {
  try {
    const companies = await getAllCompanies();
    res.json(companies);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des entreprises' });
  }
};

export const getById = async (req: Request, res: Response) => {
  try {
    const company = await getCompanyById(parseInt(req.params.id));
    if (!company) return res.status(404).json({ message: 'Entreprise non trouvée' });
    res.json(company);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération de l\'entreprise' });
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const company = await createCompany(req.body);
    res.status(201).json(company);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const update = async (req: Request, res: Response) => {
  try {
    const company = await updateCompany(parseInt(req.params.id), req.body);
    if (!company) return res.status(404).json({ message: 'Entreprise non trouvée' });
    res.json(company);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const remove = async (req: Request, res: Response) => {
  try {
    await deleteCompany(parseInt(req.params.id));
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la suppression de l\'entreprise' });
  }
};