# Script de nettoyage et optimisation du projet CRM Racha Groupe
# Usage: .\clean-project.ps1

Write-Host "🧹 NETTOYAGE ET OPTIMISATION DU PROJET" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Fichiers et dossiers à supprimer (inutiles pour la production)
$filesToRemove = @(
    # Fichiers de développement
    ".eslintcache",
    ".vscode",
    ".idea",
    "*.log",
    "*.tmp",
    
    # Fichiers de build temporaires
    "dist-ssr",
    ".vite",
    ".cache",
    
    # Fichiers de test (si présents)
    "coverage",
    "test-results",
    
    # Fichiers système
    "Thumbs.db",
    ".DS_Store",
    "desktop.ini"
)

# Dossiers node_modules (seront réinstallés sur le serveur)
$nodeModulesDirs = @(
    "node_modules",
    "backend/node_modules"
)

Write-Host "`n📁 Suppression des fichiers inutiles..." -ForegroundColor Blue

foreach ($pattern in $filesToRemove) {
    $items = Get-ChildItem -Path . -Name $pattern -Recurse -Force -ErrorAction SilentlyContinue
    foreach ($item in $items) {
        if (Test-Path $item) {
            Remove-Item $item -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "Supprimé: $item" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n📦 Nettoyage des node_modules..." -ForegroundColor Blue
foreach ($dir in $nodeModulesDirs) {
    if (Test-Path $dir) {
        Write-Host "Suppression: $dir" -ForegroundColor Yellow
        Remove-Item $dir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

Write-Host "`n🔍 Vérification de la structure optimisée..." -ForegroundColor Blue

# Fichiers essentiels qui doivent être présents
$essentialFiles = @(
    "package.json",
    "backend/package.json",
    "backend/src/app.ts",
    "src/main.tsx",
    "src/App.tsx",
    "database/schema.sql",
    ".env.production",
    "vite.config.ts",
    "tailwind.config.ts"
)

$missingFiles = @()
foreach ($file in $essentialFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file MANQUANT" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "`n⚠️  Fichiers manquants détectés!" -ForegroundColor Red
    Write-Host "Le projet pourrait ne pas fonctionner correctement." -ForegroundColor Red
    exit 1
}

Write-Host "`n📊 Taille du projet après nettoyage:" -ForegroundColor Blue
$totalSize = (Get-ChildItem -Path . -Recurse -File | Measure-Object -Property Length -Sum).Sum
$totalSizeMB = [math]::Round($totalSize / 1MB, 2)
Write-Host "Taille totale: $totalSizeMB MB" -ForegroundColor White

Write-Host "`n✅ NETTOYAGE TERMINÉ!" -ForegroundColor Green
Write-Host "Le projet est maintenant optimisé pour le déploiement." -ForegroundColor Green
