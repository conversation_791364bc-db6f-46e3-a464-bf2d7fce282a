{"name": "crm-racha-groupe", "private": true, "version": "1.0.0", "description": "Système de gestion CRM professionnel pour Racha Groupe - Solutions complètes de gestion d'entreprise", "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "preview": "npx vite preview", "start": "npx vite preview --port 3000 --host", "type-check": "npx tsc --noEmit", "lint": "npm exec eslint -- . --ext .ts,.tsx", "clean": "npx rimraf dist", "build:backend": "cd backend && npm run build", "start:backend": "cd backend && npm start", "install:all": "npm install && cd backend && npm install", "build:all": "npm run build && npm run build:backend", "start:production": "npm run build:all && concurrently \"npm run start:backend\" \"npm start\"", "deploy:vps": "npm run install:all && npm run build:all"}, "keywords": ["crm", "react", "typescript", "vite", "tailwindcss", "shadcn", "morocco", "business-management"], "author": "Racha Business Group", "license": "MIT", "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@types/qrcode": "^1.5.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.462.0", "mysql2": "^3.14.1", "next-themes": "^0.3.0", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.25.42"}, "devDependencies": {"@eslint/js": "^9.0.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.0.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^16.0.0", "postcss": "^8.4.47", "rimraf": "^6.0.1", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "vite": "^7.0.6"}}