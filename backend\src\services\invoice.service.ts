import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const db = connectDB();

const invoiceSchema = z.object({
  client: z.string().min(1),
  date: z.string().min(1),
  items: z.array(z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    quantity: z.number().min(1),
    unitPrice: z.number().min(0),
    discount: z.number().optional()
  })),
  subtotal: z.number().min(0),
  discount: z.number().optional(),
  tax: z.number().min(0),
  total: z.number().min(0),
  notes: z.string().optional(),
  status: z.string().optional()
});

export async function getAllInvoices(page: number = 1, limit: number = 10) {
  return paginateQuery('invoices', page, limit);
}

export async function getInvoiceById(id: string) {
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM invoices WHERE id = ?', [id]);
  return rows[0] || null;
}

export async function createInvoice(data: any) {
  const parse = invoiceSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { client, date, items, subtotal, discount, tax, total, notes, status } = parse.data;
  const [result] = await db.query<OkPacket>('INSERT INTO invoices (client, date, items, subtotal, discount, tax, total, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', [client, date, JSON.stringify(items), subtotal, discount, tax, total, notes, status]);
  return result.insertId;
}

export async function updateInvoice(id: string, data: any) {
  const parse = invoiceSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields: any = { ...parse.data };
  if (fields.items) {
    fields.items = JSON.stringify(fields.items);
  }
  const [result] = await db.query<OkPacket>('UPDATE invoices SET ? WHERE id = ?', [fields, id]);
  return result.affectedRows;
}

export async function deleteInvoice(id: string) {
  const [result] = await db.query<OkPacket>('DELETE FROM invoices WHERE id = ?', [id]);
  return result.affectedRows;
}