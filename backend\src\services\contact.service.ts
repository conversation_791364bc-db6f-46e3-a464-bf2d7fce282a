import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';

const db = connectDB();

const contactSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().optional(),
  company: z.string().optional(),
  address: z.string().optional(),
  notes: z.string().optional()
});

export async function getAllContacts(page: number = 1, limit: number = 10) {
  return paginateQuery('contacts', page, limit);
}

export async function getContactById(id: string) {
  const [rows] = await db.query('SELECT * FROM contacts WHERE id = ?', [id]);
  return (rows as any[])[0];
}

export async function createContact(data: any) {
  const parse = contactSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { name, email, phone, company, address, notes } = parse.data;
  const [result] = await db.query('INSERT INTO contacts (name, email, phone, company, address, notes) VALUES (?, ?, ?, ?, ?, ?)', [name, email, phone, company, address, notes]);
  return (result as any).insertId;
}

export async function updateContact(id: string, data: any) {
  const parse = contactSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields = parse.data;
  const [result] = await db.query('UPDATE contacts SET ? WHERE id = ?', [fields, id]);
  return (result as any).affectedRows;
}

export async function deleteContact(id: string) {
  const [result] = await db.query('DELETE FROM contacts WHERE id = ?', [id]);
  return (result as any).affectedRows;
}