import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const fileSchema = z.object({
  filename: z.string().min(1, 'Nom de fichier requis'),
  original_name: z.string().min(1),
  mime_type: z.string().optional(),
  file_size: z.number().optional(),
  file_path: z.string().min(1),
  entity_type: z.string().optional(),
  entity_id: z.number().optional(),
  uploaded_by: z.number().optional(),
  is_public: z.boolean().optional(),
});

export const getAllFiles = async (page: number = 1, limit: number = 10) => {
  return paginateQuery('files', page, limit);
};

export const getFileById = async (id: number) => {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM files WHERE id = ?', [id]);
  return rows[0] || null;
};

export const createFile = async (data: z.infer<typeof fileSchema>) => {
  const validatedData = fileSchema.parse(data);
  const db = connectDB();
  const [result] = await db.query<OkPacket>(
    'INSERT INTO files (filename, original_name, mime_type, file_size, file_path, entity_type, entity_id, uploaded_by, is_public) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [
      validatedData.filename,
      validatedData.original_name,
      validatedData.mime_type,
      validatedData.file_size,
      validatedData.file_path,
      validatedData.entity_type,
      validatedData.entity_id,
      validatedData.uploaded_by,
      validatedData.is_public,
    ]
  );
  return { id: result.insertId, ...validatedData };
};

export const updateFile = async (id: number, data: Partial<z.infer<typeof fileSchema>>) => {
  const validatedData = fileSchema.partial().parse(data);
  const db = connectDB();
  const updates = [];
  const values = [];
  for (const [key, value] of Object.entries(validatedData)) {
    if (value !== undefined) {
      updates.push(`${key} = ?`);
      values.push(value);
    }
  }
  if (updates.length === 0) return null;
  values.push(id);
  await db.query<OkPacket>(`UPDATE files SET ${updates.join(', ')} WHERE id = ?`, values);
  return getFileById(id);
};

export const deleteFile = async (id: number) => {
  const db = connectDB();
  await db.query<OkPacket>('DELETE FROM files WHERE id = ?', [id]);
  return { message: 'Fichier supprimé avec succès' };
};