import { Request, Response } from 'express';
import * as userService from '../services/user.service';

export async function getAllUsers(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await userService.getAllUsers(page, limit);
    res.json(result);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getUserById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const user = await userService.getUserById(id);
    if (!user) return res.status(404).json({ error: 'Utilisateur non trouvé' });
    res.json(user);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createUser(req: Request, res: Response) {
  try {
    await userService.createUser(req.body);
    // Audit log (exemple)
    res.status(201).json({ message: 'Utilisateur créé' });
  } catch (err: any) {
    res.status(400).json({ error: err.message });
  }
}

export async function updateUser(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await userService.updateUser(id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Utilisateur non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Utilisateur modifié' });
  } catch (err: any) {
    if (err.message === 'Validation failed') {
      res.status(400).json({ error: 'Validation', details: err });
    } else {
      res.status(500).json({ error: 'Erreur serveur' });
    }
  }
}

export async function deleteUser(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await userService.deleteUser(id);
    if (affected === 0) return res.status(404).json({ error: 'Utilisateur non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Utilisateur supprimé' });
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}