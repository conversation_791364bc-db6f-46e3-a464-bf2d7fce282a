[Unit]
Description=CRM Racha Groupe - Système de gestion client
Documentation=https://github.com/your-repo/crm-racha-groupe
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/var/www/crm-racha-groupe

# Scripts de gestion
ExecStart=/var/www/crm-racha-groupe/start-server.sh
ExecStop=/var/www/crm-racha-groupe/stop-server.sh
ExecReload=/var/www/crm-racha-groupe/restart-server.sh

# Fichier PID
PIDFile=/var/www/crm-racha-groupe/server.pid

# Politique de redémarrage
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Variables d'environnement
Environment=NODE_ENV=production
Environment=PORT=5001

# Sécurité
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/crm-racha-groupe/logs
ReadWritePaths=/var/www/crm-racha-groupe/uploads

# Limites de ressources
LimitNOFILE=65536
LimitNPROC=4096

# Timeout
TimeoutStartSec=60
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
