import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as quoteController from '../controllers/quote.controller';

const router = Router();

router.get('/', authenticateJWT, quoteController.getAllQuotes);
router.get('/:id', authenticateJWT, quoteController.getQuoteById);
router.post('/', authenticateJWT, authorizeRole(['admin', 'user']), quoteController.createQuote);
router.put('/:id', authenticateJWT, authorizeRole(['admin', 'user']), quoteController.updateQuote);
router.delete('/:id', authenticateJWT, authorizeRole(['admin']), quoteController.deleteQuote);

export default router; 