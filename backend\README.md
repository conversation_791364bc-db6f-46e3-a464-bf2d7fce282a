# Backend CRM Racha Groupe

## Installation

1. <PERSON><PERSON><PERSON> le dépôt (ou placer ce dossier dans le projet principal)
2. Installer les dépendances :
   ```bash
   npm install
   ```
3. Configurer le fichier `.env` (voir exemple ci-dessous)
4. Lancer en développement :
   ```bash
   npm run dev
   ```
5. Compiler pour la production :
   ```bash
   npm run build
   npm start
   ```

## Exemple de fichier `.env`
```
DB_HOST=localhost
DB_PORT=3306
DB_NAME=admin_crm
DB_USER=kiwiland
DB_PASSWORD=*H@dFcMq0q38nvrz
JWT_SECRET=racha-crm-secret-key-2024
JWT_EXPIRES_IN=24h
PORT=3001
FRONTEND_URL=https://crm.rachadigital.com
```

## Déploiement sur OVH VPS
- Utiliser PM2 ou systemd pour lancer le backend en production
- Configurer Nginx/Apache pour :
  - <PERSON>vir le frontend (build React)
  - Proxyfier `/api` vers `http://localhost:3001`

## Fonctionnalités principales
- Authentification JWT (inscription, login, refresh, rôles)
- Connexion MariaDB
- Endpoints RESTful pour toutes les entités CRM
- Upload de fichiers (PDF, images)
- Sécurité (CORS, helmet, rate limiting, validation)
- Logs d'audit

## Structure du dossier
```
backend/
├── src/
│   ├── controllers/
│   ├── middlewares/
│   ├── models/
│   ├── routes/
│   ├── services/
│   ├── utils/
│   ├── uploads/
│   └── app.ts
├── .env
├── package.json
├── tsconfig.json
└── README.md
``` 