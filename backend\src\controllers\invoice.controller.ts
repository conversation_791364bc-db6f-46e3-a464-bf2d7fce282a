import { Request, Response } from 'express';
import * as invoiceService from '../services/invoice.service';

export async function getAllInvoices(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await invoiceService.getAllInvoices(page, limit);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getInvoiceById(req: Request, res: Response) {
  try {
    const invoice = await invoiceService.getInvoiceById(req.params.id);
    if (!invoice) return res.status(404).json({ error: 'Facture non trouvée' });
    res.json(invoice);
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createInvoice(req: Request, res: Response) {
  try {
    const id = await invoiceService.createInvoice(req.body);
    res.status(201).json({ message: 'Facture créée', id });
  } catch (error: any) {
    if (error.message === 'Validation failed') {
      return res.status(400).json({ error: 'Validation', details: error.errors });
    }
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function updateInvoice(req: Request, res: Response) {
  try {
    const affected = await invoiceService.updateInvoice(req.params.id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Facture non trouvée' });
    res.json({ message: 'Facture modifiée' });
  } catch (error: any) {
    if (error.message === 'Validation failed') {
      return res.status(400).json({ error: 'Validation', details: error.errors });
    }
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function deleteInvoice(req: Request, res: Response) {
  try {
    const affected = await invoiceService.deleteInvoice(req.params.id);
    if (affected === 0) return res.status(404).json({ error: 'Facture non trouvée' });
    res.json({ message: 'Facture supprimée' });
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}