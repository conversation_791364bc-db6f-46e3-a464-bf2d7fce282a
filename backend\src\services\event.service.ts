import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';

const db = connectDB();

const eventSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  date: z.string().min(1),
  location: z.string().optional(),
  status: z.string().optional()
});

export async function getAllEvents(page: number = 1, limit: number = 10) {
  return paginateQuery('events', page, limit);
}

export async function getEventById(id: string) {
  const [rows] = await db.query('SELECT * FROM events WHERE id = ?', [id]);
  return (rows as any[])[0];
}

export async function createEvent(data: any) {
  const parse = eventSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { title, description, date, location, status } = parse.data;
  const [result] = await db.query('INSERT INTO events (title, description, date, location, status) VALUES (?, ?, ?, ?, ?)', [title, description, date, location, status]);
  return (result as any).insertId;
}

export async function updateEvent(id: string, data: any) {
  const parse = eventSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields = { ...parse.data };
  const [result] = await db.query('UPDATE events SET ? WHERE id = ?', [fields, id]);
  return (result as any).affectedRows;
}

export async function deleteEvent(id: string) {
  const [result] = await db.query('DELETE FROM events WHERE id = ?', [id]);
  return (result as any).affectedRows;
}