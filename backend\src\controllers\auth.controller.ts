import { Request, Response } from 'express';
import * as authService from '../services/auth.service';

export async function register(req: Request, res: Response) {
  try {
    await authService.register(req.body);
    res.status(201).json({ message: 'Utilisa<PERSON><PERSON> c<PERSON>' });
  } catch (err: any) {
    if (err.message === 'Email déjà utilisé') {
      res.status(409).json({ error: err.message });
    } else {
      res.status(400).json({ error: err.message });
    }
  }
}

export async function login(req: Request, res: Response) {
  try {
    const result = await authService.login(req.body);
    res.json(result);
  } catch (err: any) {
    if (err.message === 'Identifiants invalides') {
      res.status(401).json({ error: err.message });
    } else {
      res.status(400).json({ error: err.message });
    }
  }
}

export async function refreshToken(req: Request, res: Response) {
  try {
    const result = await authService.refreshToken(req.body.token);
    res.json(result);
  } catch (err: any) {
    res.status(401).json({ error: 'Token invalide' });
  }
}

export async function getProfile(req: Request, res: Response) {
  const user = (req as any).user;
  if (!user) return res.status(401).json({ error: 'Non authentifié' });
  try {
    const profile = await authService.getProfile(user.id);
    res.json(profile);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}