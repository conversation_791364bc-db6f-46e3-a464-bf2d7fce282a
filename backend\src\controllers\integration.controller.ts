import { Request, Response } from 'express';
import { getAllIntegrations, getIntegrationById, createIntegration, updateIntegration, deleteIntegration } from '../services/integration.service';

export const getAll = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const integrations = await getAllIntegrations(page, limit);
    res.json(integrations);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des intégrations' });
  }
};

export const getById = async (req: Request, res: Response) => {
  try {
    const integration = await getIntegrationById(parseInt(req.params.id));
    if (!integration) return res.status(404).json({ message: 'Intégration non trouvée' });
    res.json(integration);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération de l\'intégration' });
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const integration = await createIntegration(req.body);
    res.status(201).json(integration);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const update = async (req: Request, res: Response) => {
  try {
    const integration = await updateIntegration(parseInt(req.params.id), req.body);
    if (!integration) return res.status(404).json({ message: 'Intégration non trouvée' });
    res.json(integration);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const remove = async (req: Request, res: Response) => {
  try {
    await deleteIntegration(parseInt(req.params.id));
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la suppression de l\'intégration' });
  }
};