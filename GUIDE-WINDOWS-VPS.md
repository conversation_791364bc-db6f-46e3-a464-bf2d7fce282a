# 🪟 GUIDE WINDOWS → VPS OVH - CRM Racha Groupe

## 🎯 DÉPLOIEMENT DEPUIS WINDOWS VERS VPS LINUX

### **📋 PRÉREQUIS SUR WINDOWS**

- ✅ Node.js 18+ installé
- ✅ PowerShell 5.0+ ou PowerShell Core
- ✅ Client SSH (OpenSSH, PuTTY, ou WSL)
- ✅ Client SCP/SFTP (WinSCP, FileZilla, ou ligne de commande)

### **🚀 ÉTAPES RAPIDES**

#### **1. Build sur Windows**

```powershell
# Option A: Script PowerShell (recommandé)
.\build-windows.ps1

# Option B: Script Batch
.\build-windows.bat

# Option C: Manuel
npm install
cd backend
npm install
npm run build
cd ..
npm run build
```

#### **2. Création de l'archive**

```powershell
# Créer l'archive pour le VPS
.\create-vps-archive.ps1

# Ou avec node_modules (plus lourd mais plus sûr)
.\create-vps-archive.ps1 -IncludeNodeModules
```

#### **3. Transfert vers le VPS**

**Option A: SCP (ligne de commande)**
```powershell
# Transférer l'archive
scp crm-racha-groupe-vps.zip root@VOTRE-IP-VPS:/tmp/
```

**Option B: WinSCP (interface graphique)**
1. Ouvrir WinSCP
2. Se connecter au VPS (IP, root, mot de passe)
3. Naviguer vers `/tmp/`
4. Glisser-déposer `crm-racha-groupe-vps.zip`

**Option C: FileZilla**
1. Protocole: SFTP
2. Hôte: IP de votre VPS
3. Utilisateur: root
4. Mot de passe: votre mot de passe VPS
5. Uploader vers `/tmp/`

#### **4. Déploiement sur le VPS**

```bash
# Connexion SSH au VPS
ssh root@VOTRE-IP-VPS

# Extraction de l'archive
cd /var/www
unzip /tmp/crm-racha-groupe-vps.zip
mv crm-racha-groupe /var/www/

# Déploiement automatique
cd /var/www/crm-racha-groupe
chmod +x deploy-manual.sh
./deploy-manual.sh
```

## 🛠️ SCRIPTS WINDOWS CRÉÉS

### **1. `build-windows.ps1` - Build PowerShell**

```powershell
# Build complet avec vérifications
.\build-windows.ps1

# Build sans réinstaller les dépendances
.\build-windows.ps1 -SkipInstall

# Build avec logs détaillés
.\build-windows.ps1 -Verbose
```

**Fonctionnalités :**
- ✅ Vérification Node.js/npm
- ✅ Installation automatique des dépendances
- ✅ Build frontend + backend
- ✅ Vérification des fichiers générés
- ✅ Rapport de taille des builds

### **2. `build-windows.bat` - Build Batch**

```batch
# Build simple compatible tous Windows
.\build-windows.bat
```

**Fonctionnalités :**
- ✅ Compatible Windows XP+
- ✅ Build automatique complet
- ✅ Messages colorés (si supportés)

### **3. `create-vps-archive.ps1` - Création d'archive**

```powershell
# Archive standard (sans node_modules)
.\create-vps-archive.ps1

# Archive avec node_modules (plus sûr)
.\create-vps-archive.ps1 -IncludeNodeModules

# Archive avec nom personnalisé
.\create-vps-archive.ps1 -OutputPath "mon-crm.zip"
```

**Contenu de l'archive :**
- ✅ Code source complet
- ✅ Builds compilés (dist/, backend/dist/)
- ✅ Configuration (.env.production, package.json)
- ✅ Scripts de gestion VPS
- ✅ Documentation complète

## 🔧 DÉPANNAGE WINDOWS

### **Problème: "Execution Policy"**

```powershell
# Autoriser l'exécution des scripts PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Ou pour une session uniquement
powershell -ExecutionPolicy Bypass -File .\build-windows.ps1
```

### **Problème: "Cannot find module 'vite'"**

```powershell
# Réinstaller les dépendances
Remove-Item node_modules -Recurse -Force
Remove-Item package-lock.json -Force
npm install
```

### **Problème: "Path too long" (Windows)**

```powershell
# Activer les chemins longs
git config --system core.longpaths true

# Ou utiliser des chemins courts
subst X: .
cd X:
.\build-windows.ps1
```

### **Problème: Erreurs de compilation TypeScript**

```powershell
# Nettoyer et rebuilder
Remove-Item backend/dist -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item dist -Recurse -Force -ErrorAction SilentlyContinue
.\build-windows.ps1
```

## 📊 VÉRIFICATION DU BUILD

### **Fichiers qui doivent être générés :**

```
✅ dist/
   ├── index.html
   ├── assets/
   │   ├── *.js (fichiers JavaScript)
   │   ├── *.css (fichiers CSS)
   │   └── *.svg, *.png (images)

✅ backend/dist/
   ├── app.js
   ├── controllers/
   ├── services/
   ├── routes/
   ├── middlewares/
   └── utils/
```

### **Commande de vérification :**

```powershell
# Vérifier que tous les fichiers sont présents
if (Test-Path "dist/index.html") { Write-Host "✅ Frontend OK" } else { Write-Host "❌ Frontend manquant" }
if (Test-Path "backend/dist/app.js") { Write-Host "✅ Backend OK" } else { Write-Host "❌ Backend manquant" }
```

## 🌐 ACCÈS APRÈS DÉPLOIEMENT

Une fois le déploiement terminé sur le VPS :

- **Application** : `http://VOTRE-IP-VPS`
- **API** : `http://VOTRE-IP-VPS/api`
- **Health Check** : `http://VOTRE-IP-VPS/api/health`

## 📋 CHECKLIST COMPLÈTE

### **Sur Windows :**
- [ ] Node.js 18+ installé
- [ ] Dépendances installées (`npm install`)
- [ ] Build réussi (`.\build-windows.ps1`)
- [ ] Archive créée (`.\create-vps-archive.ps1`)
- [ ] Archive transférée sur le VPS

### **Sur le VPS :**
- [ ] Archive extraite dans `/var/www/crm-racha-groupe/`
- [ ] Script de déploiement exécuté (`./deploy-manual.sh`)
- [ ] Base de données configurée
- [ ] Nginx configuré
- [ ] Application accessible

## 🆘 SUPPORT RAPIDE

### **Erreurs courantes et solutions :**

1. **"npm ERR! code ENOENT"**
   ```powershell
   npm cache clean --force
   Remove-Item node_modules -Recurse -Force
   npm install
   ```

2. **"Cannot find package 'vite'"**
   ```powershell
   npm install vite --save-dev
   ```

3. **"Access denied" sur Windows**
   ```powershell
   # Exécuter PowerShell en tant qu'administrateur
   ```

4. **Build lent sur Windows**
   ```powershell
   # Exclure node_modules de l'antivirus
   # Utiliser un SSD si possible
   ```

Cette approche vous garantit un déploiement réussi depuis Windows vers votre VPS OVH ! 🚀
