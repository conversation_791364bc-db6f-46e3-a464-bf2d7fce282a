#!/bin/bash

# Script de déploiement pour VPS OVH - CRM Racha Groupe
# Usage: ./deploy-vps.sh

set -e

echo "🚀 Déploiement CRM Racha Groupe sur VPS OVH"
echo "=============================================="

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Vérifications préliminaires
log "Vérification de l'environnement..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    error "Node.js n'est pas installé. Veuillez installer Node.js 18+ avant de continuer."
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    error "Node.js version 18+ requis. Version actuelle: $(node -v)"
fi

# Vérifier npm
if ! command -v npm &> /dev/null; then
    error "npm n'est pas installé."
fi

# Vérifier MySQL/MariaDB
if ! command -v mysql &> /dev/null; then
    warning "MySQL/MariaDB CLI non trouvé. Assurez-vous que la base de données est accessible."
fi

log "✅ Environnement vérifié"

# Nettoyer les anciens builds
log "Nettoyage des anciens builds..."
rm -rf dist/
rm -rf backend/dist/
log "✅ Nettoyage terminé"

# Installation des dépendances
log "Installation des dépendances..."
npm install
if [ $? -ne 0 ]; then
    error "Échec de l'installation des dépendances frontend"
fi

cd backend
npm install
if [ $? -ne 0 ]; then
    error "Échec de l'installation des dépendances backend"
fi
cd ..
log "✅ Dépendances installées"

# Vérification de la configuration
log "Vérification de la configuration..."
if [ ! -f ".env" ]; then
    error "Fichier .env manquant. Copiez .env.example vers .env et configurez-le."
fi

# Vérifier les variables critiques
if ! grep -q "DB_HOST" .env; then
    error "Variable DB_HOST manquante dans .env"
fi

if ! grep -q "DB_PASSWORD" .env; then
    error "Variable DB_PASSWORD manquante dans .env"
fi

log "✅ Configuration vérifiée"

# Test de connexion à la base de données
log "Test de connexion à la base de données..."
DB_HOST=$(grep DB_HOST .env | cut -d '=' -f2)
DB_USER=$(grep DB_USER .env | cut -d '=' -f2)
DB_PASSWORD=$(grep DB_PASSWORD .env | cut -d '=' -f2)
DB_NAME=$(grep DB_NAME .env | cut -d '=' -f2)

if command -v mysql &> /dev/null; then
    mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT 1;" &> /dev/null
    if [ $? -eq 0 ]; then
        log "✅ Connexion base de données OK"
    else
        warning "⚠️  Impossible de se connecter à la base de données. Vérifiez la configuration."
    fi
else
    warning "⚠️  MySQL CLI non disponible, impossible de tester la connexion DB"
fi

# Build du backend
log "Compilation du backend..."
cd backend
npm run build
if [ $? -ne 0 ]; then
    error "Échec de la compilation du backend"
fi
cd ..
log "✅ Backend compilé"

# Build du frontend
log "Compilation du frontend..."
npm run build
if [ $? -ne 0 ]; then
    error "Échec de la compilation du frontend"
fi
log "✅ Frontend compilé"

# Vérification des builds
log "Vérification des builds..."
if [ ! -d "dist" ]; then
    error "Dossier dist/ du frontend non trouvé"
fi

if [ ! -d "backend/dist" ]; then
    error "Dossier backend/dist/ non trouvé"
fi

if [ ! -f "backend/dist/app.js" ]; then
    error "Fichier backend/dist/app.js non trouvé"
fi

log "✅ Builds vérifiés"

# Création du script de démarrage
log "Création du script de démarrage..."
cat > start-production.sh << 'EOF'
#!/bin/bash

# Script de démarrage pour production
export NODE_ENV=production

echo "🚀 Démarrage CRM Racha Groupe en production..."

# Démarrer le backend
cd backend
node dist/app.js &
BACKEND_PID=$!

echo "Backend démarré (PID: $BACKEND_PID)"
echo "Application disponible sur: http://localhost:5001"
echo "API disponible sur: http://localhost:5001/api"

# Attendre que l'utilisateur arrête le processus
wait $BACKEND_PID
EOF

chmod +x start-production.sh
log "✅ Script de démarrage créé"

# Résumé du déploiement
echo ""
echo "🎉 DÉPLOIEMENT TERMINÉ AVEC SUCCÈS!"
echo "===================================="
echo ""
info "📁 Fichiers générés:"
echo "   - dist/ (frontend compilé)"
echo "   - backend/dist/ (backend compilé)"
echo "   - start-production.sh (script de démarrage)"
echo ""
info "🚀 Pour démarrer l'application:"
echo "   ./start-production.sh"
echo ""
info "🌐 URLs d'accès:"
echo "   - Application: http://votre-serveur:5001"
echo "   - API: http://votre-serveur:5001/api"
echo "   - Health check: http://votre-serveur:5001/api/health"
echo ""
info "📋 Prochaines étapes:"
echo "   1. Configurez votre serveur web (Nginx/Apache) si nécessaire"
echo "   2. Configurez un gestionnaire de processus (PM2/systemd)"
echo "   3. Configurez SSL/HTTPS"
echo "   4. Configurez les sauvegardes de base de données"
echo ""
warning "⚠️  N'oubliez pas de:"
echo "   - Sécuriser votre fichier .env"
echo "   - Configurer le firewall"
echo "   - Mettre en place la surveillance"
echo ""
