# Script de validation finale avant déploiement VPS
# Usage: .\validate-deployment.ps1

param(
    [switch]$Detailed = $false
)

function Write-Success($message) { Write-Host "✅ $message" -ForegroundColor Green }
function Write-Error($message) { Write-Host "❌ $message" -ForegroundColor Red }
function Write-Warning($message) { Write-Host "⚠️  $message" -ForegroundColor Yellow }
function Write-Info($message) { Write-Host "ℹ️  $message" -ForegroundColor Blue }
function Write-Header($message) { 
    Write-Host "`n🔍 $message" -ForegroundColor Cyan
    Write-Host ("=" * 50) -ForegroundColor Cyan
}

Write-Host "🎯 VALIDATION FINALE - CRM Racha Groupe" -ForegroundColor Magenta
Write-Host "=======================================" -ForegroundColor Magenta

$errors = @()
$warnings = @()

# 1. Vérification des fichiers source essentiels
Write-Header "Fichiers source essentiels"

$essentialSourceFiles = @(
    @{ Path = "package.json"; Description = "Configuration frontend" },
    @{ Path = "backend/package.json"; Description = "Configuration backend" },
    @{ Path = "backend/src/app.ts"; Description = "Application backend principale" },
    @{ Path = "src/main.tsx"; Description = "Point d'entrée frontend" },
    @{ Path = "src/App.tsx"; Description = "Composant principal React" },
    @{ Path = "database/schema.sql"; Description = "Schéma base de données" },
    @{ Path = "vite.config.ts"; Description = "Configuration Vite" },
    @{ Path = "tailwind.config.ts"; Description = "Configuration Tailwind" }
)

foreach ($file in $essentialSourceFiles) {
    if (Test-Path $file.Path) {
        Write-Success "$($file.Path) - $($file.Description)"
    } else {
        Write-Error "$($file.Path) MANQUANT - $($file.Description)"
        $errors += "Fichier manquant: $($file.Path)"
    }
}

# 2. Vérification de la configuration d'environnement
Write-Header "Configuration d'environnement"

if (Test-Path ".env.production") {
    Write-Success ".env.production trouvé"
    
    # Vérifier les variables critiques
    $envContent = Get-Content ".env.production" -Raw
    $criticalVars = @("DB_HOST", "DB_PASSWORD", "JWT_SECRET", "PORT")
    
    foreach ($var in $criticalVars) {
        if ($envContent -match "$var=") {
            Write-Success "Variable $var configurée"
        } else {
            Write-Warning "Variable $var manquante dans .env.production"
            $warnings += "Variable d'environnement manquante: $var"
        }
    }
} else {
    Write-Error ".env.production manquant"
    $errors += "Fichier .env.production manquant"
}

# 3. Vérification des scripts de gestion VPS
Write-Header "Scripts de gestion VPS"

$vpsScripts = @(
    "start-server.sh",
    "stop-server.sh", 
    "restart-server.sh",
    "server-status.sh",
    "deploy-manual.sh"
)

foreach ($script in $vpsScripts) {
    if (Test-Path $script) {
        Write-Success "$script présent"
    } else {
        Write-Error "$script manquant"
        $errors += "Script VPS manquant: $script"
    }
}

# 4. Vérification des builds (si présents)
Write-Header "Builds compilés"

if (Test-Path "dist") {
    if (Test-Path "dist/index.html") {
        Write-Success "Build frontend présent et valide"
        
        if ($Detailed) {
            $frontendSize = (Get-ChildItem -Path "dist" -Recurse | Measure-Object -Property Length -Sum).Sum
            $frontendSizeMB = [math]::Round($frontendSize/1MB, 2)
            Write-Info "Taille frontend: $frontendSizeMB MB"
        }
    } else {
        Write-Warning "Build frontend incomplet (index.html manquant)"
        $warnings += "Build frontend incomplet"
    }
} else {
    Write-Warning "Build frontend manquant - sera généré sur le serveur"
}

if (Test-Path "backend/dist") {
    if (Test-Path "backend/dist/app.js") {
        Write-Success "Build backend présent et valide"
        
        if ($Detailed) {
            $backendSize = (Get-ChildItem -Path "backend/dist" -Recurse | Measure-Object -Property Length -Sum).Sum
            $backendSizeMB = [math]::Round($backendSize/1MB, 2)
            Write-Info "Taille backend: $backendSizeMB MB"
        }
    } else {
        Write-Warning "Build backend incomplet (app.js manquant)"
        $warnings += "Build backend incomplet"
    }
} else {
    Write-Warning "Build backend manquant - sera généré sur le serveur"
}

# 5. Vérification de la structure des dossiers
Write-Header "Structure des dossiers"

$essentialDirs = @(
    @{ Path = "backend/src"; Description = "Code source backend" },
    @{ Path = "src"; Description = "Code source frontend" },
    @{ Path = "public"; Description = "Assets statiques" },
    @{ Path = "database"; Description = "Scripts base de données" }
)

foreach ($dir in $essentialDirs) {
    if (Test-Path $dir.Path -PathType Container) {
        $fileCount = (Get-ChildItem -Path $dir.Path -Recurse -File).Count
        Write-Success "$($dir.Path) - $fileCount fichiers"
    } else {
        Write-Error "$($dir.Path) MANQUANT - $($dir.Description)"
        $errors += "Dossier manquant: $($dir.Path)"
    }
}

# 6. Vérification des dépendances critiques
Write-Header "Dépendances critiques"

try {
    $frontendPkg = Get-Content "package.json" | ConvertFrom-Json
    $backendPkg = Get-Content "backend/package.json" | ConvertFrom-Json
    
    # Vérifier les dépendances frontend critiques
    $frontendCritical = @("react", "react-dom", "vite", "@vitejs/plugin-react-swc")
    foreach ($dep in $frontendCritical) {
        if ($frontendPkg.dependencies.$dep -or $frontendPkg.devDependencies.$dep) {
            Write-Success "Frontend: $dep configuré"
        } else {
            Write-Warning "Frontend: $dep manquant"
            $warnings += "Dépendance frontend manquante: $dep"
        }
    }
    
    # Vérifier les dépendances backend critiques
    $backendCritical = @("express", "mysql2", "cors", "dotenv")
    foreach ($dep in $backendCritical) {
        if ($backendPkg.dependencies.$dep) {
            Write-Success "Backend: $dep configuré"
        } else {
            Write-Warning "Backend: $dep manquant"
            $warnings += "Dépendance backend manquante: $dep"
        }
    }
} catch {
    Write-Error "Erreur lors de la lecture des package.json"
    $errors += "Erreur package.json: $($_.Exception.Message)"
}

# 7. Calcul de la taille totale du projet
Write-Header "Taille du projet"

$totalSize = (Get-ChildItem -Path . -Recurse -File -Exclude "node_modules" | Measure-Object -Property Length -Sum).Sum
$totalSizeMB = [math]::Round($totalSize / 1MB, 2)
Write-Info "Taille totale (sans node_modules): $totalSizeMB MB"

if ($totalSizeMB -gt 100) {
    Write-Warning "Projet volumineux ($totalSizeMB MB) - le transfert pourrait être lent"
    $warnings += "Projet volumineux: $totalSizeMB MB"
}

# 8. Résumé final
Write-Header "RÉSUMÉ DE LA VALIDATION"

if ($errors.Count -eq 0 -and $warnings.Count -eq 0) {
    Write-Host "`n🎉 VALIDATION RÉUSSIE!" -ForegroundColor Green
    Write-Host "Votre projet est prêt pour le déploiement VPS!" -ForegroundColor Green
    
    Write-Info "`n📋 Prochaines etapes:"
    Write-Info "1. Executer: .\create-vps-archive.ps1"
    Write-Info "2. Transferer l'archive sur votre VPS"
    Write-Info "3. Suivre le guide: INSTALLATION-MANUELLE-VPS.md"
    
} elseif ($errors.Count -eq 0) {
    Write-Host "`n⚠️  VALIDATION AVEC AVERTISSEMENTS" -ForegroundColor Yellow
    Write-Host "Le projet peut etre deploye mais avec des avertissements:" -ForegroundColor Yellow
    
    foreach ($warning in $warnings) {
        Write-Warning $warning
    }
    
} else {
    Write-Host "`n❌ VALIDATION ECHOUEE" -ForegroundColor Red
    Write-Host "Corrigez les erreurs suivantes avant le deploiement:" -ForegroundColor Red
    
    foreach ($error in $errors) {
        Write-Error $error
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "`nAvertissements:" -ForegroundColor Yellow
        foreach ($warning in $warnings) {
            Write-Warning $warning
        }
    }
    
    exit 1
}

Write-Host "`n📖 Guides disponibles:" -ForegroundColor Blue
Write-Host "- STRUCTURE-FICHIERS-VPS.md" -ForegroundColor White
Write-Host "- INSTALLATION-MANUELLE-VPS.md" -ForegroundColor White
Write-Host "- GUIDE-WINDOWS-VPS.md" -ForegroundColor White
