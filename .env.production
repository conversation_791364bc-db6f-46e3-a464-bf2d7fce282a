# Configuration de production pour CRM Racha Groupe sur VPS OVH
# Copiez ce fichier vers .env et modifiez les valeurs selon votre configuration

# Mode environnement
NODE_ENV=production

# Configuration serveur
PORT=5001

# URLs de l'application
VITE_APP_BASE_URL=https://crm.rachadigital.com
VITE_API_URL=https://crm.rachadigital.com/api
VITE_FRONTEND_URL=https://crm.rachadigital.com
FRONTEND_URL=https://crm.rachadigital.com
API_BASE_URL=https://crm.rachadigital.com/api

# Configuration base de données
DB_HOST=localhost
DB_PORT=3306
DB_NAME=admin_crm
DB_USER=kiwiland
DB_PASSWORD=*H@dFcMq0q38nvrz

# Sécurité JWT
JWT_SECRET=racha-crm-secret-key-2024-production-change-this
JWT_EXPIRES_IN=24h

# Configuration application
VITE_APP_NAME="CRM Racha Groupe"
VITE_APP_VERSION="2.0.0"
VITE_JWT_SECRET=racha-crm-secret-key-2024-production-change-this
VITE_AUTH_TOKEN_EXPIRY=86400000
VITE_AUTH_REFRESH_THRESHOLD=300000

# Configuration fichiers
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,application/pdf

# Configuration notifications
VITE_NOTIFICATION_TIMEOUT=5000
VITE_MAX_NOTIFICATIONS=3

# Configuration cache
VITE_CACHE_DURATION=3600000
VITE_CACHE_MAX_SIZE=50

# Configuration sécurité
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_DEBUG_MODE=false
DEBUG_MODE=false

# Configuration CORS
CORS_ORIGIN=https://crm.rachadigital.com

# Configuration WhatsApp Business
VITE_WHATSAPP_BUSINESS_NUMBER=+212669382828

# Configuration email (optionnel)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# Configuration backup (optionnel)
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
