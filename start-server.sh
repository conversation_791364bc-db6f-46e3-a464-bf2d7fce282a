#!/bin/bash

# Script de démarrage pour CRM Racha Groupe (Alternative à PM2)
# Usage: ./start-server.sh

# Configuration
APP_DIR="/var/www/crm-racha-groupe"
PID_FILE="$APP_DIR/server.pid"
LOG_FILE="$APP_DIR/logs/server.log"
ERROR_LOG="$APP_DIR/logs/error.log"

# Couleurs pour les logs
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Fonction de log avec couleurs
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}" | tee -a "$LOG_FILE"
}

# Vérification des prérequis
if [ ! -d "$APP_DIR" ]; then
    error "Dossier application non trouvé: $APP_DIR"
    exit 1
fi

if [ ! -f "$APP_DIR/backend/dist/app.js" ]; then
    error "Application backend non compilée. Exécutez: cd backend && npm run build"
    exit 1
fi

if [ ! -f "$APP_DIR/.env" ]; then
    error "Fichier .env manquant. Copiez .env.production vers .env"
    exit 1
fi

# Création des dossiers de logs si nécessaire
mkdir -p "$APP_DIR/logs"
mkdir -p "$APP_DIR/uploads"

# Vérifier si le serveur est déjà en cours
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        warning "Le serveur est déjà en cours d'exécution (PID: $PID)"
        info "Pour redémarrer, utilisez: ./restart-server.sh"
        exit 1
    else
        warning "Fichier PID obsolète trouvé, suppression..."
        rm -f "$PID_FILE"
    fi
fi

# Test de connexion à la base de données
log "Test de connexion à la base de données..."
cd "$APP_DIR"
if ! node -e "
const mysql = require('mysql2/promise');
require('dotenv').config();
(async () => {
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    await connection.execute('SELECT 1');
    await connection.end();
    console.log('✅ Connexion DB OK');
  } catch (err) {
    console.error('❌ Erreur DB:', err.message);
    process.exit(1);
  }
})();
" 2>/dev/null; then
    error "Impossible de se connecter à la base de données"
    error "Vérifiez votre configuration .env et que MySQL est démarré"
    exit 1
fi

# Démarrage du serveur
log "🚀 Démarrage du serveur CRM Racha Groupe..."

cd "$APP_DIR/backend"

# Variables d'environnement
export NODE_ENV=production
export PORT=5001

# Chargement des variables depuis .env
if [ -f "../.env" ]; then
    export $(grep -v '^#' ../.env | xargs)
fi

# Démarrage en arrière-plan avec nohup
nohup node dist/app.js >> "$LOG_FILE" 2>> "$ERROR_LOG" &
SERVER_PID=$!

# Sauvegarde du PID
echo $SERVER_PID > "$PID_FILE"

log "Serveur démarré avec le PID: $SERVER_PID"
info "Logs disponibles dans: $LOG_FILE"
info "Erreurs disponibles dans: $ERROR_LOG"
info "Application accessible sur: http://localhost:5001"

# Vérification que le serveur a bien démarré
log "Vérification du démarrage..."
sleep 5

if ps -p $SERVER_PID > /dev/null 2>&1; then
    # Test de l'API
    if curl -s http://localhost:5001/api/health > /dev/null 2>&1; then
        log "✅ Serveur démarré avec succès et API accessible"
        info "🌐 URLs d'accès:"
        info "   - Application: http://votre-serveur:5001"
        info "   - API: http://votre-serveur:5001/api"
        info "   - Health check: http://votre-serveur:5001/api/health"
        info ""
        info "📋 Commandes utiles:"
        info "   - Arrêter: ./stop-server.sh"
        info "   - Redémarrer: ./restart-server.sh"
        info "   - Statut: ./server-status.sh"
        info "   - Logs: tail -f logs/server.log"
    else
        warning "Serveur démarré mais API non accessible"
        info "Vérifiez les logs: tail -f $ERROR_LOG"
    fi
else
    error "❌ Erreur lors du démarrage du serveur"
    error "Consultez les logs d'erreur: $ERROR_LOG"
    rm -f "$PID_FILE"
    exit 1
fi
