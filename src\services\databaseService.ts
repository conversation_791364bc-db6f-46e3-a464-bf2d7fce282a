/**
 * Service de synchronisation avec la base de données MySQL/MariaDB
 * Racha Business CRM
 */

import {
  API_ENDPOINTS,
  buildApiUrl,
  getAuthHeaders,
  QueryResult,
  PaginatedResult,
  PaginationParams,
  ENV_CONFIG
} from '@/config/database';
import { toast } from 'sonner';

export class DatabaseService {
  private static instance: DatabaseService;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Effectuer une requête HTTP vers l'API
   */
  private async makeRequest<T>(
    method: string,
    url: string,
    data?: any
  ): Promise<QueryResult<T>> {
    try {
      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(),
        ...(data && { body: JSON.stringify(data) })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Database request error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Erreur inconnue' 
      };
    }
  }

  /**
   * CRUD générique pour toutes les entités
   */
  public async create<T>(entity: string, data: any): Promise<QueryResult<T>> {
    const endpoint = this.getEndpointForEntity(entity);
    return this.makeRequest<T>('POST', buildApiUrl(endpoint), data);
  }

  public async update<T>(entity: string, id: string, data: any): Promise<QueryResult<T>> {
    const endpoint = this.getEndpointForEntity(entity).replace(':id', id);
    return this.makeRequest<T>('PUT', buildApiUrl(endpoint), data);
  }

  public async delete<T>(entity: string, id: string): Promise<QueryResult<T>> {
    const endpoint = this.getEndpointForEntity(entity).replace(':id', id);
    return this.makeRequest<T>('DELETE', buildApiUrl(endpoint));
  }

  public async fetch<T>(entity: string, params?: PaginationParams & Record<string, any>): Promise<QueryResult<PaginatedResult<T>>> {
    const endpoint = this.getEndpointForEntity(entity);
    let url = buildApiUrl(endpoint);
    if (params) {
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined) {
          searchParams.append(key, String(params[key]));
        }
      });
      url += `?${searchParams.toString()}`;
    }
    return this.makeRequest<PaginatedResult<T>>('GET', url);
  }

  public async testConnection(): Promise<boolean> {
    try {
      const url = buildApiUrl('/api/health');
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders()
      });
      console.log('Health check status:', response.status);
      return response.status >= 200 && response.status < 400;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  public getSyncStats() {
    return {
      queueSize: 0,
      lastSync: null,
      isOnline: navigator.onLine
    };
  }

  private getEndpointForEntity(entity: string): string {
    const endpoints: Record<string, string> = {
      contacts: API_ENDPOINTS.contacts.list,
      quotes: API_ENDPOINTS.quotes.list,
      invoices: API_ENDPOINTS.invoices.list,
      bonLivraison: API_ENDPOINTS.bonLivraison.list,
      products: API_ENDPOINTS.products.list,
      services: API_ENDPOINTS.services.list,
      tasks: API_ENDPOINTS.tasks.list,
      events: API_ENDPOINTS.events.list,
      users: API_ENDPOINTS.users.list
    };
    return endpoints[entity] || '/api/generic';
  }

  public async exportLocalDataToDatabase(): Promise<boolean> {
    try {
      const entities = ['contacts', 'quotes', 'invoices', 'bonLivraison', 'products', 'services', 'tasks', 'events', 'users'];
      for (const entity of entities) {
        const key = `crm_${entity}`;
        const localItems = JSON.parse(localStorage.getItem(key) || '[]');
        for (const item of localItems) {
          if (item.id) {
            await this.update(entity, item.id, item);
          } else {
            await this.create(entity, item);
          }
        }
      }
      // Clear local after sync? Or keep as cache.
      // For now, keep.
      console.log('Export completed');
      return true;
    } catch (error) {
      console.error('Export failed:', error);
      return false;
    }
  }

  public async importDataFromDatabase(): Promise<boolean> {
    try {
      const entities = ['contacts', 'quotes', 'invoices', 'bonLivraison', 'products', 'services', 'tasks', 'events', 'users'];
      for (const entity of entities) {
        const result = await this.fetch(entity);
        if (result.success) {
          localStorage.setItem(`crm_${entity}`, JSON.stringify(result.data.items || []));
        }
      }
      console.log('Import completed');
      return true;
    } catch (error) {
      console.error('Import failed:', error);
      return false;
    }
  }

  public async syncData(entityName: string, action: 'create' | 'update' | 'delete', data: any): Promise<boolean> {
    try {
      if (navigator.onLine) {
        let result;
        if (action === 'create') {
          result = await this.create(entityName, data);
        } else if (action === 'update') {
          result = await this.update(entityName, data.id, data);
        } else if (action === 'delete') {
          result = await this.delete(entityName, data.id);
        }
        if (result.success) {
          // Update localStorage after successful API call
          const key = `crm_${entityName}`;
          let items = JSON.parse(localStorage.getItem(key) || '[]');
          if (action === 'create') {
            items.push({ ...data, id: result.data.id }); // Assume API returns id
          } else if (action === 'update') {
            items = items.map(item => item.id === data.id ? data : item);
          } else if (action === 'delete') {
            items = items.filter(item => item.id !== data.id);
          }
          localStorage.setItem(key, JSON.stringify(items));
          return true;
        } else {
          throw new Error('API call failed');
        }
      } else {
        // Offline: just update local
        const key = `crm_${entityName}`;
        let items = JSON.parse(localStorage.getItem(key) || '[]');
        if (action === 'create') {
          items.push(data);
        } else if (action === 'update') {
          items = items.map(item => item.id === data.id ? data : item);
        } else if (action === 'delete') {
          items = items.filter(item => item.id !== data.id);
        }
        localStorage.setItem(key, JSON.stringify(items));
        // TODO: Add to sync queue for later
        return true;
      }
    } catch (error) {
      console.error('Sync failed:', error);
      return false;
    }
  }
}

// Instance singleton
export const databaseService = DatabaseService.getInstance();
