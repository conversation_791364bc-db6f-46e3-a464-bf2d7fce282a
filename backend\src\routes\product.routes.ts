import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as productController from '../controllers/product.controller';

const router = Router();

router.get('/', authenticateJWT, productController.getAllProducts);
router.get('/:id', authenticateJWT, productController.getProductById);
router.post('/', authenticateJWT, authorizeRole(['admin', 'user']), productController.createProduct);
router.put('/:id', authenticateJWT, authorizeRole(['admin', 'user']), productController.updateProduct);
router.delete('/:id', authenticateJWT, authorize<PERSON><PERSON>(['admin']), productController.deleteProduct);

export default router; 