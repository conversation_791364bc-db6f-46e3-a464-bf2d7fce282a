# Script de build pour Windows - CRM Racha Groupe
# Usage: .\build-windows.ps1

param(
    [switch]$SkipInstall = $false,
    [switch]$Verbose = $false
)

# Configuration des couleurs
$ErrorActionPreference = "Stop"

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Info($message) {
    Write-ColorOutput Blue "ℹ️  $message"
}

function Write-Header($message) {
    Write-ColorOutput Cyan "`n🔍 $message"
    Write-ColorOutput Cyan ("=" * 50)
}

# En-tête
Write-ColorOutput Magenta @"
🚀 BUILD CRM RACHA GROUPE - Windows
====================================
"@

try {
    # Étape 1: Vérification de l'environnement
    Write-Header "Vérification de l'environnement"
    
    # Vérifier Node.js
    try {
        $nodeVersion = node --version
        Write-Success "Node.js version: $nodeVersion"
        
        # Vérifier la version (doit être 18+)
        $majorVersion = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
        if ($majorVersion -lt 18) {
            throw "Version Node.js trop ancienne. Requis: 18+, Actuel: $majorVersion"
        }
    }
    catch {
        Write-Error "Node.js non trouvé ou version incompatible"
        Write-Info "Installez Node.js 18+ depuis https://nodejs.org/"
        exit 1
    }
    
    # Vérifier npm
    try {
        $npmVersion = npm --version
        Write-Success "npm version: $npmVersion"
    }
    catch {
        Write-Error "npm non trouvé"
        exit 1
    }
    
    # Vérifier les fichiers requis
    $requiredFiles = @(
        "package.json",
        "backend/package.json",
        "backend/src/app.ts",
        ".env"
    )
    
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Success "Fichier trouvé: $file"
        } else {
            Write-Error "Fichier manquant: $file"
            if ($file -eq ".env") {
                Write-Info "Copiez .env.production vers .env et configurez-le"
            }
            exit 1
        }
    }
    
    if (-not $SkipInstall) {
        # Étape 2: Installation des dépendances frontend
        Write-Header "Installation des dépendances frontend"
        
        Write-Info "Installation en cours..."
        npm install
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors de l'installation des dépendances frontend"
        }
        Write-Success "Dépendances frontend installées"
        
        # Étape 3: Installation des dépendances backend
        Write-Header "Installation des dépendances backend"
        
        Push-Location backend
        try {
            Write-Info "Installation en cours..."
            npm install
            if ($LASTEXITCODE -ne 0) {
                throw "Erreur lors de l'installation des dépendances backend"
            }
            Write-Success "Dépendances backend installées"
        }
        finally {
            Pop-Location
        }
    } else {
        Write-Warning "Installation des dépendances ignorée (--SkipInstall)"
    }
    
    # Étape 4: Compilation du backend
    Write-Header "Compilation du backend"
    
    Push-Location backend
    try {
        Write-Info "Compilation TypeScript en cours..."
        npm run build
        if ($LASTEXITCODE -ne 0) {
            throw "Erreur lors de la compilation du backend"
        }
        
        # Vérifier que le fichier de sortie existe
        if (Test-Path "dist/app.js") {
            Write-Success "Backend compilé avec succès"
        } else {
            throw "Fichier de sortie backend non trouvé"
        }
    }
    finally {
        Pop-Location
    }
    
    # Étape 5: Compilation du frontend
    Write-Header "Compilation du frontend"
    
    Write-Info "Compilation Vite en cours..."
    npx vite build
    if ($LASTEXITCODE -ne 0) {
        throw "Erreur lors de la compilation du frontend"
    }
    
    # Vérifier que le dossier de sortie existe
    if (Test-Path "dist/index.html") {
        Write-Success "Frontend compilé avec succès"
    } else {
        throw "Fichier de sortie frontend non trouvé"
    }
    
    # Résumé final
    Write-Header "Build terminé avec succès!"
    
    Write-Success "✅ Tous les builds sont terminés"
    Write-Info ""
    Write-Info "📁 Fichiers générés:"
    Write-Info "   - dist/ (frontend compilé)"
    Write-Info "   - backend/dist/ (backend compilé)"
    Write-Info ""
    
    # Taille des builds
    $frontendSize = (Get-ChildItem -Path "dist" -Recurse | Measure-Object -Property Length -Sum).Sum
    $backendSize = (Get-ChildItem -Path "backend/dist" -Recurse | Measure-Object -Property Length -Sum).Sum
    
    Write-Info "📊 Taille des builds:"
    Write-Info "   - Frontend: $([math]::Round($frontendSize/1MB, 2)) MB"
    Write-Info "   - Backend: $([math]::Round($backendSize/1MB, 2)) MB"
    Write-Info ""
    
    Write-ColorOutput Green @"
🎉 BUILD RÉUSSI!
================

📋 Prochaines étapes pour le déploiement VPS:

1. 📦 Créer une archive avec les fichiers compilés
2. 📤 Transférer sur votre serveur VPS OVH
3. 📖 Suivre le guide: INSTALLATION-MANUELLE-VPS.md
4. 🚀 Exécuter: ./deploy-manual.sh

🔗 Guides disponibles:
   - INSTALLATION-MANUELLE-VPS.md
   - STRUCTURE-FICHIERS-VPS.md
   - PROBLEMES-IDENTIFIES.md
"@

}
catch {
    Write-Error "Erreur durant le build: $($_.Exception.Message)"
    Write-Info ""
    Write-Info "🔧 Dépannage:"
    Write-Info "   - Vérifiez que Node.js 18+ est installé"
    Write-Info "   - Vérifiez que le fichier .env existe"
    Write-Info "   - Supprimez node_modules/ et relancez"
    Write-Info "   - Consultez les logs d'erreur ci-dessus"
    exit 1
}
