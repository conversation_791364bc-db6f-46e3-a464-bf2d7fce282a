import { z } from 'zod';
import { connectDB } from '../utils/db';
import { paginateQuery } from '../utils/db';

const db = connectDB();

const quoteSchema = z.object({
  client: z.string().min(1),
  date: z.string().min(1),
  items: z.array(z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    quantity: z.number().min(1),
    unitPrice: z.number().min(0),
    discount: z.number().optional()
  })),
  subtotal: z.number().min(0),
  discount: z.number().optional(),
  tax: z.number().min(0),
  total: z.number().min(0),
  notes: z.string().optional(),
  status: z.string().optional()
});

export async function getAllQuotes(page: number = 1, limit: number = 10) {
  return paginateQuery('quotes', page, limit);
}

export async function getQuoteById(id: string) {
  const [rows] = await db.query('SELECT * FROM quotes WHERE id = ?', [id]);
  return (rows as any[])[0];
}

export async function createQuote(data: any) {
  const parse = quoteSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { client, date, items, subtotal, discount, tax, total, notes, status } = parse.data;
  const [result] = await db.query('INSERT INTO quotes (client, date, items, subtotal, discount, tax, total, notes, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', [client, date, JSON.stringify(items), subtotal, discount, tax, total, notes, status]);
  return (result as any).insertId;
}

export async function updateQuote(id: string, data: any) {
  const parse = quoteSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields: any = { ...parse.data };
  if (fields.items) {
    fields.items = JSON.stringify(fields.items);
  }
  const [result] = await db.query('UPDATE quotes SET ? WHERE id = ?', [fields, id]);
  return (result as any).affectedRows;
}

export async function deleteQuote(id: string) {
  const [result] = await db.query('DELETE FROM quotes WHERE id = ?', [id]);
  return (result as any).affectedRows;
}