# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/

# Environment files
.env
.env.local
.env.development.local
.env.production.local

# Cache directories
.cache/
.parcel-cache/
.vite/
.eslintcache
.stylelintcache

# TypeScript
*.tsbuildinfo

# Lock files (keep package-lock.json)
yarn.lock
pnpm-lock.yaml
bun.lockb

# Deployment
.vercel/
.netlify/
.wrangler/

# Database
*.db
*.sqlite
*.sqlite3

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo
*~

# OS files
Thumbs.db
ehthumbs.db
.Spotlight-V100
.Trashes

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Coverage (removed - no tests)

# Generated files
version.json
cleanup-report.json
backup-report-*.json
connection-test-report.json

# Backup files
backups/
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Deployment logs
deploy-*.log
deployment-*.json

# Cache and temporary
.npm/
.yarn/
.pnpm/
.turbo/
