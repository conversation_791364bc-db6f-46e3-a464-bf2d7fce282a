import { Request, Response } from 'express';
import * as contactService from '../services/contact.service';

export async function getAllContacts(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await contactService.getAllContacts(page, limit);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getContactById(req: Request, res: Response) {
  try {
    const contact = await contactService.getContactById(req.params.id);
    if (!contact) return res.status(404).json({ error: 'Contact non trouvé' });
    res.json(contact);
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createContact(req: Request, res: Response) {
  try {
    const id = await contactService.createContact(req.body);
    res.status(201).json({ message: 'Contact créé', id });
  } catch (error: any) {
    if (error.message === 'Validation failed') {
      return res.status(400).json({ error: 'Validation', details: error.errors });
    }
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function updateContact(req: Request, res: Response) {
  try {
    const affected = await contactService.updateContact(req.params.id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Contact non trouvé' });
    res.json({ message: 'Contact modifié' });
  } catch (error: any) {
    if (error.message === 'Validation failed') {
      return res.status(400).json({ error: 'Validation', details: error.errors });
    }
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function deleteContact(req: Request, res: Response) {
  try {
    const affected = await contactService.deleteContact(req.params.id);
    if (affected === 0) return res.status(404).json({ error: 'Contact non trouvé' });
    res.json({ message: 'Contact supprimé' });
  } catch (error) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}