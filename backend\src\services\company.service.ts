import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const companySchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  industry: z.string().optional(),
  size: z.enum(['startup', 'small', 'medium', 'large', 'enterprise']).optional(),
  website: z.string().url().optional(),
  description: z.string().optional(),
  logo_url: z.string().optional(),
});

export const getAllCompanies = async (page: number = 1, limit: number = 10) => {
  return paginateQuery('companies', page, limit);
};

export const getCompanyById = async (id: number) => {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM companies WHERE id = ?', [id]);
  return rows[0] || null;
};

export const createCompany = async (data: z.infer<typeof companySchema>) => {
  const validatedData = companySchema.parse(data);
  const db = connectDB();
  const [result] = await db.query<OkPacket>(
    'INSERT INTO companies (name, industry, size, website, description, logo_url) VALUES (?, ?, ?, ?, ?, ?)',
    [
      validatedData.name,
      validatedData.industry,
      validatedData.size,
      validatedData.website,
      validatedData.description,
      validatedData.logo_url,
    ]
  );
  return { id: result.insertId, ...validatedData };
};

export const updateCompany = async (id: number, data: Partial<z.infer<typeof companySchema>>) => {
  const validatedData = companySchema.partial().parse(data);
  const db = connectDB();
  const updates = [];
  const values = [];
  for (const [key, value] of Object.entries(validatedData)) {
    if (value !== undefined) {
      updates.push(`${key} = ?`);
      values.push(value);
    }
  }
  if (updates.length === 0) return null;
  values.push(id);
  await db.query<OkPacket>(`UPDATE companies SET ${updates.join(', ')} WHERE id = ?`, values);
  return getCompanyById(id);
};

export const deleteCompany = async (id: number) => {
  const db = connectDB();
  await db.query<OkPacket>('DELETE FROM companies WHERE id = ?', [id]);
  return { message: 'Entreprise supprimée avec succès' };
};