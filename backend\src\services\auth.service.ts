import bcrypt from 'bcryptjs';
import jwt, { Secret } from 'jsonwebtoken';
import { connectDB } from '../utils/db';
import { RowDataPacket } from 'mysql2/promise';

const JWT_SECRET: Secret = process.env.JWT_SECRET!;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

export async function register(data: { email: string, password: string, name: string, role?: string }) {
  const db = connectDB();
  const { email, password, name, role } = data;
  if (!email || !password || !name) {
    throw new Error('Champs requis manquants');
  }
  const [rows] = await db.query<RowDataPacket[]>('SELECT id FROM users WHERE email = ?', [email]);
  if (rows.length > 0) {
    throw new Error('Email déjà utilisé');
  }
  const hash = await bcrypt.hash(password, 12);
  await db.query('INSERT INTO users (email, password, name, role) VALUES (?, ?, ?, ?)', [email, hash, name, role || 'user']);
  return true;
}

export async function login(data: { email: string, password: string }) {
  const db = connectDB();
  const { email, password } = data;
  if (!email || !password) {
    throw new Error('Champs requis manquants');
  }
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM users WHERE email = ?', [email]);
  const user = rows[0];
  if (!user) {
    throw new Error('Identifiants invalides');
  }
  const valid = await bcrypt.compare(password, user.password);
  if (!valid) {
    throw new Error('Identifiants invalides');
  }
  const payload = { id: user.id, email: user.email, role: user.role };
  // @ts-ignore
  const token = jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN as string });
  return { token, user: payload };
}

export async function refreshToken(token: string) {
  if (!token) throw new Error('Token manquant');
  const decoded = jwt.verify(token, JWT_SECRET) as any;
  // @ts-ignore
  const newToken = jwt.sign({ id: decoded.id, email: decoded.email, role: decoded.role }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN as string });
  return { token: newToken };
}

export async function getProfile(userId: string) {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT id, email, name, role FROM users WHERE id = ?', [userId]);
  return rows[0];
}