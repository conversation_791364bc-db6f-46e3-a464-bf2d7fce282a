import { z } from 'zod';
import { connectDB } from '../utils/db';
import bcrypt from 'bcryptjs';
import { RowDataPacket, OkPacket } from 'mysql2/promise';



const userSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  password: z.string().min(6).optional(),
  role: z.enum(['admin', 'user']).default('user')
});

export async function getAllUsers(page: number = 1, limit: number = 10) {
  const db = connectDB();
  const offset = (page - 1) * limit;
  const [rows] = await db.query<RowDataPacket[]>('SELECT id, name, email, role FROM users LIMIT ? OFFSET ?', [limit, offset]);
  const [countRows] = await db.query<RowDataPacket[]>('SELECT COUNT(*) as total FROM users');
  const total = countRows[0].total;
  return { data: rows, total, page, limit };
}

export async function getUserById(id: string) {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT id, name, email, role FROM users WHERE id = ?', [id]);
  return rows[0] || null;
}

export async function createUser(data: any) {
  const db = connectDB();
  const parse = userSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { name, email, password, role } = parse.data;
  if (!password) throw new Error('Password required');
  const [exists] = await db.query<RowDataPacket[]>('SELECT id FROM users WHERE email = ?', [email]);
  if (exists.length > 0) throw new Error('Email already used');
  const hash = await bcrypt.hash(password, 12);
  const [result] = await db.query<OkPacket>('INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)', [name, email, hash, role]);
  return { id: result.insertId };
}

export async function updateUser(id: string, data: any) {
  const db = connectDB();
  const parse = userSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields = parse.data;
  if (fields.password) {
    fields.password = await bcrypt.hash(fields.password, 12);
  }
  const [result] = await db.query<OkPacket>('UPDATE users SET ? WHERE id = ?', [fields, id]);
  return result.affectedRows;
}

export async function deleteUser(id: string) {
  const db = connectDB();
  const [result] = await db.query<OkPacket>('DELETE FROM users WHERE id = ?', [id]);
  return result.affectedRows;
}