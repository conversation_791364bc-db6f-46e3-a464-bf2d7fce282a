import { Request, Response } from 'express';
import * as productService from '../services/product.service';

export async function getAllProducts(req: Request, res: Response) {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const result = await productService.getAllProducts(page, limit);
    res.json(result);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function getProductById(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const product = await productService.getProductById(id);
    if (!product) return res.status(404).json({ error: 'Produit non trouvé' });
    res.json(product);
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}

export async function createProduct(req: Request, res: Response) {
  try {
    const id = await productService.createProduct(req.body);
    // Audit log (exemple)
    res.status(201).json({ message: 'Produit créé', id });
  } catch (err: any) {
    res.status(400).json({ error: err.message });
  }
}

export async function updateProduct(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await productService.updateProduct(id, req.body);
    if (affected === 0) return res.status(404).json({ error: 'Produit non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Produit modifié' });
  } catch (err: any) {
    if (err.message === 'Validation failed') {
      res.status(400).json({ error: 'Validation', details: err });
    } else {
      res.status(500).json({ error: 'Erreur serveur' });
    }
  }
}

export async function deleteProduct(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const affected = await productService.deleteProduct(id);
    if (affected === 0) return res.status(404).json({ error: 'Produit non trouvé' });
    // Audit log (exemple)
    res.json({ message: 'Produit supprimé' });
  } catch (err: any) {
    res.status(500).json({ error: 'Erreur serveur' });
  }
}