import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';

const db = connectDB();

const serviceSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  category: z.string().optional(),
  price: z.number().min(0),
  status: z.string().optional()
});

export async function getAllServices(page: number = 1, limit: number = 10) {
  return paginateQuery('services', page, limit);
}

export async function getServiceById(id: string) {
  const [rows] = await db.query('SELECT * FROM services WHERE id = ?', [id]);
  return (rows as any[])[0];
}

export async function createService(data: any) {
  const parse = serviceSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { name, description, category, price, status } = parse.data;
  const [result] = await db.query('INSERT INTO services (name, description, category, price, status) VALUES (?, ?, ?, ?, ?)', [name, description, category, price, status]);
  return (result as any).insertId;
}

export async function updateService(id: string, data: any) {
  const parse = serviceSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields = { ...parse.data };
  const [result] = await db.query('UPDATE services SET ? WHERE id = ?', [fields, id]);
  return (result as any).affectedRows;
}

export async function deleteService(id: string) {
  const [result] = await db.query('DELETE FROM services WHERE id = ?', [id]);
  return (result as any).affectedRows;
}