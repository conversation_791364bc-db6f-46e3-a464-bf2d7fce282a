import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';
import { RowDataPacket, OkPacket } from 'mysql2/promise';

const integrationSchema = z.object({
  type: z.string().min(1, 'Type requis'),
  name: z.string().min(1, 'Nom requis'),
  config: z.object({}).optional(), // JSON
  is_active: z.boolean().optional(),
  last_sync: z.date().optional(),
  created_by: z.number().optional(),
});

export const getAllIntegrations = async (page: number = 1, limit: number = 10) => {
  return paginateQuery('integrations', page, limit);
};

export const getIntegrationById = async (id: number) => {
  const db = connectDB();
  const [rows] = await db.query<RowDataPacket[]>('SELECT * FROM integrations WHERE id = ?', [id]);
  return rows[0] || null;
};

export const createIntegration = async (data: z.infer<typeof integrationSchema>) => {
  const validatedData = integrationSchema.parse(data);
  const db = connectDB();
  const [result] = await db.query<OkPacket>(
    'INSERT INTO integrations (type, name, config, is_active, last_sync, created_by) VALUES (?, ?, ?, ?, ?, ?)',
    [
      validatedData.type,
      validatedData.name,
      JSON.stringify(validatedData.config),
      validatedData.is_active,
      validatedData.last_sync,
      validatedData.created_by,
    ]
  );
  return { id: result.insertId, ...validatedData };
};

export const updateIntegration = async (id: number, data: Partial<z.infer<typeof integrationSchema>>) => {
  const validatedData = integrationSchema.partial().parse(data);
  const db = connectDB();
  const updates = [];
  const values = [];
  for (const [key, value] of Object.entries(validatedData)) {
    if (value !== undefined) {
      updates.push(`${key} = ?`);
      values.push(key === 'config' ? JSON.stringify(value) : value);
    }
  }
  if (updates.length === 0) return null;
  values.push(id);
  const [result] = await db.query<OkPacket>(`UPDATE integrations SET ${updates.join(', ')} WHERE id = ?`, values);
  return result.affectedRows > 0 ? getIntegrationById(id) : null;
};

export const deleteIntegration = async (id: number) => {
  const db = connectDB();
  const [result] = await db.query<OkPacket>('DELETE FROM integrations WHERE id = ?', [id]);
  return result.affectedRows > 0 ? { message: 'Intégration supprimée avec succès' } : null;
};