import { Request, Response } from 'express';
import { getAllBonLivraisons, getBonLivraisonById, createBonLivraison, updateBonLivraison, deleteBonLivraison } from '../services/bon_livraison.service';

export const getAll = async (req: Request, res: Response) => {
  try {
    const bonLivraisons = await getAllBonLivraisons();
    res.json(bonLivraisons);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des bons de livraison' });
  }
};

export const getById = async (req: Request, res: Response) => {
  try {
    const bonLivraison = await getBonLivraisonById(parseInt(req.params.id));
    if (!bonLivraison) return res.status(404).json({ message: 'Bon de livraison non trouvé' });
    res.json(bonLivraison);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération du bon de livraison' });
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const bonLivraison = await createBonLivraison(req.body);
    res.status(201).json(bonLivraison);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const update = async (req: Request, res: Response) => {
  try {
    const bonLivraison = await updateBonLivraison(parseInt(req.params.id), req.body);
    if (!bonLivraison) return res.status(404).json({ message: 'Bon de livraison non trouvé' });
    res.json(bonLivraison);
  } catch (error: any) {
    res.status(400).json({ message: error.message });
  }
};

export const remove = async (req: Request, res: Response) => {
  try {
    await deleteBonLivraison(parseInt(req.params.id));
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la suppression du bon de livraison' });
  }
};