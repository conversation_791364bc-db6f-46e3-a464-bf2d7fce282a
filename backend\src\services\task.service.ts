import { z } from 'zod';
import { connectDB, paginateQuery } from '../utils/db';

const db = connectDB();

const taskSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  dueDate: z.string().optional(),
  status: z.string().optional(),
  assignedTo: z.string().optional()
});

export async function getAllTasks(page: number = 1, limit: number = 10) {
  return paginateQuery('tasks', page, limit);
}

export async function getTaskById(id: string) {
  const [rows] = await db.query('SELECT * FROM tasks WHERE id = ?', [id]);
  return (rows as any[])[0];
}

export async function createTask(data: any) {
  const parse = taskSchema.safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const { title, description, dueDate, status, assignedTo } = parse.data;
  const [result] = await db.query('INSERT INTO tasks (title, description, dueDate, status, assignedTo) VALUES (?, ?, ?, ?, ?)', [title, description, dueDate, status, assignedTo]);
  return (result as any).insertId;
}

export async function updateTask(id: string, data: any) {
  const parse = taskSchema.partial().safeParse(data);
  if (!parse.success) throw new Error('Validation failed');
  const fields = { ...parse.data };
  const [result] = await db.query('UPDATE tasks SET ? WHERE id = ?', [fields, id]);
  return (result as any).affectedRows;
}

export async function deleteTask(id: string) {
  const [result] = await db.query('DELETE FROM tasks WHERE id = ?', [id]);
  return (result as any).affectedRows;
}