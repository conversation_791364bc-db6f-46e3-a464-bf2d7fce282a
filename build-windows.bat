@echo off
echo ========================================
echo BUILD CRM RACHA GROUPE - Windows
echo ========================================

:: Couleurs (si supportées)
set GREEN=[92m
set RED=[91m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

echo %BLUE%Etape 1/4: Verification des dependances...%NC%

:: Vérifier Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Erreur: Node.js non trouve%NC%
    echo Installez Node.js 18+ avant de continuer
    pause
    exit /b 1
)

echo %GREEN%Node.js version:%NC%
node --version

:: Vérifier npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%Erreur: npm non trouve%NC%
    pause
    exit /b 1
)

echo %GREEN%npm version:%NC%
npm --version

echo.
echo %BLUE%Etape 2/4: Installation des dependances frontend...%NC%

:: Installation des dépendances frontend
npm install
if %errorlevel% neq 0 (
    echo %RED%Erreur lors de l'installation des dependances frontend%NC%
    pause
    exit /b 1
)

echo %GREEN%Dependances frontend installees%NC%

echo.
echo %BLUE%Etape 3/4: Installation des dependances backend...%NC%

:: Installation des dépendances backend
cd backend
npm install
if %errorlevel% neq 0 (
    echo %RED%Erreur lors de l'installation des dependances backend%NC%
    pause
    exit /b 1
)

echo %GREEN%Dependances backend installees%NC%

echo.
echo %BLUE%Etape 4/4: Compilation...%NC%

:: Build backend
echo Compilation du backend...
npm run build
if %errorlevel% neq 0 (
    echo %RED%Erreur lors de la compilation du backend%NC%
    pause
    exit /b 1
)

echo %GREEN%Backend compile avec succes%NC%

:: Retour au dossier racine
cd ..

:: Build frontend
echo Compilation du frontend...
npx vite build
if %errorlevel% neq 0 (
    echo %RED%Erreur lors de la compilation du frontend%NC%
    pause
    exit /b 1
)

echo %GREEN%Frontend compile avec succes%NC%

echo.
echo %GREEN%========================================%NC%
echo %GREEN%BUILD TERMINE AVEC SUCCES!%NC%
echo %GREEN%========================================%NC%
echo.
echo %BLUE%Fichiers generes:%NC%
echo - dist/ (frontend compile)
echo - backend/dist/ (backend compile)
echo.
echo %BLUE%Prochaines etapes:%NC%
echo 1. Copiez ces dossiers sur votre serveur VPS
echo 2. Suivez le guide INSTALLATION-MANUELLE-VPS.md
echo 3. Executez deploy-manual.sh sur le serveur
echo.
echo %YELLOW%Appuyez sur une touche pour continuer...%NC%
pause >nul
