import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as serviceController from '../controllers/service.controller';

const router = Router();

router.get('/', authenticateJWT, serviceController.getAllServices);
router.get('/:id', authenticateJWT, serviceController.getServiceById);
router.post('/', authenticateJWT, authorizeRole(['admin', 'user']), serviceController.createService);
router.put('/:id', authenticateJWT, authorizeRole(['admin', 'user']), serviceController.updateService);
router.delete('/:id', authenticateJWT, authorizeRole(['admin']), serviceController.deleteService);

export default router; 