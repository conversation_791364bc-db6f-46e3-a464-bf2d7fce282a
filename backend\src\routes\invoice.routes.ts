import { Router } from 'express';
import { authenticateJWT } from '../middlewares/authenticateJWT';
import { authorizeRole } from '../middlewares/authorizeRole';
import * as invoiceController from '../controllers/invoice.controller';

const router = Router();

router.get('/', authenticateJWT, invoiceController.getAllInvoices);
router.get('/:id', authenticateJWT, invoiceController.getInvoiceById);
router.post('/', authenticateJWT, authorizeRole(['admin', 'user']), invoiceController.createInvoice);
router.put('/:id', authenticateJWT, authorizeRole(['admin', 'user']), invoiceController.updateInvoice);
router.delete('/:id', authenticateJWT, authorizeRole(['admin']), invoiceController.deleteInvoice);

export default router; 